#!/usr/bin/env node
/**
 * Test API with the provided authentication token
 */

const http = require('http');

const TOKEN = 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.pKVGoeEkMTM9OOhiOaXiFAUYFV0Jd5qV7ynhnMVaCE-KjNY_c3K-GHuLwMrMIFtuU14YdWx7gb33yvK0C0dimN2rPIHz7xXN2uW1HqT_iwg_TR5y9wOvCrYq9hzyXCAbcd3DFTcYF9BADJ99ZzyPZ-YPPuu3oCcgKjnMPMIamJ999GwcLVBYTnvQbYk_ISpgoIEj3SWaT8rpGEVlAP_AlbbhUEFpYIfZYw3r7S1B8b8XoLKYFW0K_8IBAs10Becxgk0dBuPHGBufjkuhvBPXRJP4KNjAMuIBjaWefN8ZbxadGfSBSLNRE256bEDgUOQH2Yhnj5CffeN0ueP2qkpdeg';

function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${TOKEN}`
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: responseData });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testAPI() {
  console.log('🔑 Testing API with Authentication Token');
  console.log('=' .repeat(50));
  
  try {
    // Test health endpoint
    console.log('1. Testing health endpoint...');
    const health = await makeRequest('/api/v1/robots/data/health');
    console.log(`   Status: ${health.status}`);
    console.log(`   Response: ${JSON.stringify(health.data)}`);
    
    // Test connection endpoint
    console.log('\n2. Testing ROS2 connection...');
    const connection = await makeRequest('/api/v1/robots/test/connection', 'POST');
    console.log(`   Status: ${connection.status}`);
    console.log(`   Response: ${JSON.stringify(connection.data, null, 2)}`);
    
    if (connection.status === 201 && connection.data.success) {
      console.log('   ✅ ROS2 connection test successful!');
      
      // Test save map
      console.log('\n3. Testing Save Map...');
      const saveMap = await makeRequest('/api/v1/robots/map/save', 'POST');
      console.log(`   Status: ${saveMap.status}`);
      console.log(`   Response: ${JSON.stringify(saveMap.data, null, 2)}`);
      
      if (saveMap.status === 201 && saveMap.data.success) {
        console.log('   ✅ Save Map successful!');
        console.log(`   📁 File: ${saveMap.data.filename}`);
        console.log(`   📏 Size: ${(saveMap.data.file_size / 1024).toFixed(1)} KB`);
      }
      
    } else {
      console.log('   ❌ ROS2 connection failed');
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }
  
  console.log('\n' + '=' .repeat(50));
  console.log('🎯 Test Complete');
  console.log('If successful, the frontend should work with this token!');
}

testAPI();
