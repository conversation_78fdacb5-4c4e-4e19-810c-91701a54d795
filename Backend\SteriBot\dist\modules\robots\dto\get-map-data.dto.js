"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MapDataResponseDto = exports.MapStatisticsDto = exports.MapInfoDto = exports.MapOriginDto = exports.GetMapDataDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class GetMapDataDto {
}
exports.GetMapDataDto = GetMapDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'IP address of the ROS2 bridge to connect to',
        example: '*************',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIP)(),
    __metadata("design:type", String)
], GetMapDataDto.prototype, "ip_address", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Port of the ROS2 bridge (optional, defaults to 8765)',
        example: 8765,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], GetMapDataDto.prototype, "port", void 0);
class MapOriginDto {
}
exports.MapOriginDto = MapOriginDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Position of the map origin',
        type: 'object',
        properties: {
            x: { type: 'number', example: -10.0 },
            y: { type: 'number', example: -10.0 },
            z: { type: 'number', example: 0.0 },
        },
    }),
    __metadata("design:type", Object)
], MapOriginDto.prototype, "position", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Orientation of the map origin',
        type: 'object',
        properties: {
            x: { type: 'number', example: 0.0 },
            y: { type: 'number', example: 0.0 },
            z: { type: 'number', example: 0.0 },
            w: { type: 'number', example: 1.0 },
        },
    }),
    __metadata("design:type", Object)
], MapOriginDto.prototype, "orientation", void 0);
class MapInfoDto {
}
exports.MapInfoDto = MapInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Map width in pixels',
        example: 384,
    }),
    __metadata("design:type", Number)
], MapInfoDto.prototype, "width", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Map height in pixels',
        example: 384,
    }),
    __metadata("design:type", Number)
], MapInfoDto.prototype, "height", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Map resolution in meters per pixel',
        example: 0.05,
    }),
    __metadata("design:type", Number)
], MapInfoDto.prototype, "resolution", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Map origin pose',
        type: MapOriginDto,
    }),
    __metadata("design:type", MapOriginDto)
], MapInfoDto.prototype, "origin", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp when the map was created',
        example: '2025-07-30T10:30:00.000Z',
    }),
    __metadata("design:type", String)
], MapInfoDto.prototype, "map_load_time", void 0);
class MapStatisticsDto {
}
exports.MapStatisticsDto = MapStatisticsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of cells in the map',
        example: 147456,
    }),
    __metadata("design:type", Number)
], MapStatisticsDto.prototype, "total_cells", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of free space cells (value = 0)',
        example: 98304,
    }),
    __metadata("design:type", Number)
], MapStatisticsDto.prototype, "free_cells", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of occupied cells (value = 100)',
        example: 12288,
    }),
    __metadata("design:type", Number)
], MapStatisticsDto.prototype, "occupied_cells", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of unknown cells (value = -1)',
        example: 36864,
    }),
    __metadata("design:type", Number)
], MapStatisticsDto.prototype, "unknown_cells", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Percentage of free space',
        example: 66.7,
    }),
    __metadata("design:type", Number)
], MapStatisticsDto.prototype, "free_percentage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Percentage of occupied space',
        example: 8.3,
    }),
    __metadata("design:type", Number)
], MapStatisticsDto.prototype, "occupied_percentage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Percentage of unknown space',
        example: 25.0,
    }),
    __metadata("design:type", Number)
], MapStatisticsDto.prototype, "unknown_percentage", void 0);
class MapDataResponseDto {
}
exports.MapDataResponseDto = MapDataResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Map metadata information',
        type: MapInfoDto,
        required: false,
    }),
    __metadata("design:type", MapInfoDto)
], MapDataResponseDto.prototype, "info", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Occupancy grid data array. Values: -1=unknown, 0=free, 100=occupied',
        type: [Number],
        example: [0, 0, -1, 100, 0, -1],
        required: false,
    }),
    __metadata("design:type", Array)
], MapDataResponseDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Map statistics and analysis',
        type: MapStatisticsDto,
        required: false,
    }),
    __metadata("design:type", MapStatisticsDto)
], MapDataResponseDto.prototype, "statistics", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Connection status to the ROS2 bridge',
        enum: ['connected', 'failed', 'timeout', 'disconnected'],
        example: 'connected',
    }),
    __metadata("design:type", String)
], MapDataResponseDto.prototype, "connection_status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp when map data was retrieved',
        example: '2025-07-30T10:30:00.000Z',
    }),
    __metadata("design:type", String)
], MapDataResponseDto.prototype, "last_updated", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Error message if retrieval failed',
        example: 'Connection timeout',
        required: false,
    }),
    __metadata("design:type", String)
], MapDataResponseDto.prototype, "error_message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'IP address that was queried',
        example: '*************',
    }),
    __metadata("design:type", String)
], MapDataResponseDto.prototype, "queried_ip", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Port that was queried',
        example: 8765,
    }),
    __metadata("design:type", Number)
], MapDataResponseDto.prototype, "queried_port", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Whether map data was successfully retrieved',
        example: true,
    }),
    __metadata("design:type", Boolean)
], MapDataResponseDto.prototype, "has_map_data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Size of the map data array',
        example: 147456,
    }),
    __metadata("design:type", Number)
], MapDataResponseDto.prototype, "data_length", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Summary description of the map',
        example: 'Map: 384x384 pixels, 0.05m/pixel, 147456 cells',
        required: false,
    }),
    __metadata("design:type", String)
], MapDataResponseDto.prototype, "summary", void 0);
//# sourceMappingURL=get-map-data.dto.js.map