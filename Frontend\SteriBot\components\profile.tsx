"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Edit, Download, Shield, Bell, LogOut, Loader2, AlertCircle } from "lucide-react"

interface UserProfile {
  userId: string;
  username: string;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  language: string;
  jobTitle: {
    id: string;
    name: string;
    description: string;
  };
  department: {
    id: string;
    name: string;
    description: string;
  };
  picture: string;
  lastLogin: string | null;
}

export function Profile() {
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch user profile data
  useEffect(() => {
    const fetchProfile = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Get auth token from localStorage
        const customToken = localStorage.getItem('customToken');
        const authToken = localStorage.getItem('authToken');

        if (!customToken && !authToken) {
          throw new Error('No authentication token found');
        }

        const response = await fetch('http://localhost:3001/api/v1/auth/profile', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${customToken || authToken}`,
          },
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch profile: ${response.status}`);
        }

        const profileData = await response.json();
        setUserProfile(profileData);
      } catch (err) {
        console.error('Error fetching profile:', err);
        setError(err instanceof Error ? err.message : 'Failed to load profile');
      } finally {
        setIsLoading(false);
      }
    };

    fetchProfile();
  }, []);

  // Helper function to format dates
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return 'Invalid date';
    }
  };

  // Get user initials for avatar
  const getUserInitials = (firstName: string, lastName: string, email: string) => {
    if (firstName && lastName) {
      return (firstName[0] + lastName[0]).toUpperCase();
    }
    if (firstName) {
      return firstName.slice(0, 2).toUpperCase();
    }
    return email.slice(0, 2).toUpperCase();
  };

  // Get full name
  const getFullName = (firstName: string, lastName: string, username: string) => {
    if (firstName && lastName) {
      return `${firstName} ${lastName}`;
    }
    if (firstName) {
      return firstName;
    }
    return username;
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="p-6 space-y-6 bg-teal-50 min-h-screen">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex flex-col items-center gap-4">
            <Loader2 className="w-8 h-8 animate-spin text-teal-600" />
            <p className="text-teal-600">Loading profile...</p>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="p-6 space-y-6 bg-teal-50 min-h-screen">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex flex-col items-center gap-4 text-center">
            <AlertCircle className="w-8 h-8 text-red-500" />
            <p className="text-red-600">Error loading profile</p>
            <p className="text-sm text-gray-600">{error}</p>
            <Button
              onClick={() => window.location.reload()}
              variant="outline"
              className="text-teal-600 border-teal-600 hover:bg-teal-50"
            >
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // No profile data
  if (!userProfile) {
    return (
      <div className="p-6 space-y-6 bg-teal-50 min-h-screen">
        <div className="flex items-center justify-center min-h-[400px]">
          <p className="text-gray-600">No profile data available</p>
        </div>
      </div>
    );
  }
  return (
    <div className="p-6 space-y-6 bg-teal-50 min-h-screen">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-teal-800">Profile</h1>
        <Button variant="outline" className="text-teal-600 border-teal-600 hover:bg-teal-50 bg-transparent">
          <Edit className="w-4 h-4 mr-2" />
          Edit Profile
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Profile Info */}
        <div className="space-y-6">
          {/* Main Profile Card */}
          <Card className=" text-white" style={{ background: 'linear-gradient(90deg, #0A3F4C, #0C6980)' }}>
            <CardContent className="p-6">
              <div className="flex flex-col items-center text-center">
                <Avatar className="w-24 h-24 mb-4">
                  <AvatarImage src={userProfile.picture} alt={getFullName(userProfile.firstName, userProfile.lastName, userProfile.username)} />
                  <AvatarFallback className="bg-teal-600 text-white text-xl">
                    {getUserInitials(userProfile.firstName, userProfile.lastName, userProfile.email)}
                  </AvatarFallback>
                </Avatar>
                <h2 className="text-xl font-bold mb-2">
                  {getFullName(userProfile.firstName, userProfile.lastName, userProfile.username)}
                </h2>
                <p className="text-teal-100 text-sm">@{userProfile.username}</p>
                <p className="text-teal-200 text-sm">{userProfile.jobTitle.name}</p>
              </div>
            </CardContent>
          </Card>

          {/* Account Status */}
          <Card>
            <CardHeader>
              <CardTitle>Account Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Account Status</span>
                <Badge className="bg-green-100 text-green-800 hover:bg-current">
                  Active
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Department</span>
                <span className="font-medium">{userProfile.department.name}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Job Title</span>
                <span className="font-medium">{userProfile.jobTitle.name}</span>
              </div>
             
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Access Level</span>
                <Badge variant="secondary" className="bg-blue-100 text-blue-800 capitalize">
                  {userProfile.role}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Language</span>
                <span className="font-medium">{userProfile.language.toUpperCase()}</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Details and Actions */}
        <div className="space-y-6">
          {/* Details Card */}
          <Card>
            <CardHeader>
              <CardTitle>Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm text-gray-600">First Name</label>
                  <p className="font-medium">{userProfile.firstName}</p>
                </div>
                <div>
                  <label className="text-sm text-gray-600">Last Name</label>
                  <p className="font-medium">{userProfile.lastName}</p>
                </div>
              </div>
              <div>
                <label className="text-sm text-gray-600">Email</label>
                <p className="font-medium">{userProfile.email}</p>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm text-gray-600">Username</label>
                  <p className="font-medium">{userProfile.username}</p>
                </div>
                <div>
                  <label className="text-sm text-gray-600">User ID</label>
                  <p className="font-medium text-xs break-all">{userProfile.userId}</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm text-gray-600">Job Title</label>
                  <p className="font-medium">{userProfile.jobTitle.name}</p>
                </div>
                <div>
                  <label className="text-sm text-gray-600">Department</label>
                  <p className="font-medium">{userProfile.department.name}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Security */}
          <Card>
            <CardHeader>
              <CardTitle>Security</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">Email Address</p>
                  <p className="text-sm text-gray-600">{userProfile.email}</p>
                </div>
              </div>

              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">Current Password</p>
                  <p className="text-sm text-gray-600">••••••••••••</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full justify-start bg-transparent">
                <Download className="w-4 h-4 mr-2" />
                Download Activity Report
              </Button>
              <Button variant="outline" className="w-full justify-start bg-transparent">
                <Shield className="w-4 h-4 mr-2" />
                Two-Factor Authentication
              </Button>
              <Button variant="outline" className="w-full justify-start bg-transparent">
                <Bell className="w-4 h-4 mr-2" />
                Notification Settings
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50 bg-transparent"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Sign Out
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
