import { type OnModuleInit } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import * as admin from "firebase-admin";
export declare class FirebaseService implements OnModuleInit {
    private configService;
    private firestore;
    private auth;
    private storage;
    constructor(configService: ConfigService);
    onModuleInit(): Promise<void>;
    getFirestore(): admin.firestore.Firestore;
    getAuth(): admin.auth.Auth;
    getStorage(): admin.storage.Storage;
    uploadFile(buffer: Buffer, fileName: string, contentType: string, folder?: string): Promise<string>;
    deleteFile(filePath: string): Promise<void>;
    extractFilePathFromUrl(url: string): string;
}
