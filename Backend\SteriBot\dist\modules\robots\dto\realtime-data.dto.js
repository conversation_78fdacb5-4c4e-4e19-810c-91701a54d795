"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RealtimeStreamStatusDto = exports.RealtimeMapDataEventDto = exports.RealtimeRobotDataEventDto = exports.RealtimeConnectionEventDto = exports.StopRealtimeStreamDto = exports.StartRealtimeStreamDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class StartRealtimeStreamDto {
}
exports.StartRealtimeStreamDto = StartRealtimeStreamDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'IP address of the ROS2 bridge to connect to',
        example: '*************',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIP)(),
    __metadata("design:type", String)
], StartRealtimeStreamDto.prototype, "ip_address", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Port of the ROS2 bridge (optional, defaults to 9090)',
        example: 9090,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], StartRealtimeStreamDto.prototype, "port", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Include robot position and battery data in stream',
        example: true,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], StartRealtimeStreamDto.prototype, "include_robot_data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Include map data in stream',
        example: true,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], StartRealtimeStreamDto.prototype, "include_map_data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Stream update frequency in Hz (1-10)',
        example: 2,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], StartRealtimeStreamDto.prototype, "update_frequency", void 0);
class StopRealtimeStreamDto {
}
exports.StopRealtimeStreamDto = StopRealtimeStreamDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Stream session ID to stop',
        example: 'stream_*************_9090',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], StopRealtimeStreamDto.prototype, "session_id", void 0);
class RealtimeConnectionEventDto {
}
exports.RealtimeConnectionEventDto = RealtimeConnectionEventDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Event type',
        enum: ['connected', 'disconnected', 'connecting', 'error', 'reconnecting'],
        example: 'connected',
    }),
    __metadata("design:type", String)
], RealtimeConnectionEventDto.prototype, "event", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Stream session ID',
        example: 'stream_*************_9090',
    }),
    __metadata("design:type", String)
], RealtimeConnectionEventDto.prototype, "session_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'IP address being streamed',
        example: '*************',
    }),
    __metadata("design:type", String)
], RealtimeConnectionEventDto.prototype, "ip_address", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Port being used',
        example: 9090,
    }),
    __metadata("design:type", Number)
], RealtimeConnectionEventDto.prototype, "port", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of the event',
        example: '2025-07-30T10:30:00.000Z',
    }),
    __metadata("design:type", String)
], RealtimeConnectionEventDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Error message if event is error',
        required: false,
    }),
    __metadata("design:type", String)
], RealtimeConnectionEventDto.prototype, "error_message", void 0);
class RealtimeRobotDataEventDto {
}
exports.RealtimeRobotDataEventDto = RealtimeRobotDataEventDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Event type',
        example: 'robot_data',
    }),
    __metadata("design:type", String)
], RealtimeRobotDataEventDto.prototype, "event", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Stream session ID',
        example: 'stream_*************_9090',
    }),
    __metadata("design:type", String)
], RealtimeRobotDataEventDto.prototype, "session_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Robot position data',
    }),
    __metadata("design:type", Object)
], RealtimeRobotDataEventDto.prototype, "position", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Battery level data',
    }),
    __metadata("design:type", Object)
], RealtimeRobotDataEventDto.prototype, "battery_level", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp when data was received',
        example: '2025-07-30T10:30:00.000Z',
    }),
    __metadata("design:type", String)
], RealtimeRobotDataEventDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data sequence number for ordering',
        example: 1234,
    }),
    __metadata("design:type", Number)
], RealtimeRobotDataEventDto.prototype, "sequence", void 0);
class RealtimeMapDataEventDto {
}
exports.RealtimeMapDataEventDto = RealtimeMapDataEventDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Event type',
        example: 'map_data',
    }),
    __metadata("design:type", String)
], RealtimeMapDataEventDto.prototype, "event", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Stream session ID',
        example: 'stream_*************_9090',
    }),
    __metadata("design:type", String)
], RealtimeMapDataEventDto.prototype, "session_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Map metadata information',
    }),
    __metadata("design:type", Object)
], RealtimeMapDataEventDto.prototype, "info", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Occupancy grid data array (compressed for real-time)',
        type: [Number],
        required: false,
    }),
    __metadata("design:type", Array)
], RealtimeMapDataEventDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Map statistics',
    }),
    __metadata("design:type", Object)
], RealtimeMapDataEventDto.prototype, "statistics", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Whether this is a full map update or incremental',
        example: true,
    }),
    __metadata("design:type", Boolean)
], RealtimeMapDataEventDto.prototype, "is_full_update", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp when map data was received',
        example: '2025-07-30T10:30:00.000Z',
    }),
    __metadata("design:type", String)
], RealtimeMapDataEventDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Map data sequence number',
        example: 42,
    }),
    __metadata("design:type", Number)
], RealtimeMapDataEventDto.prototype, "sequence", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Size of the map data array',
        example: 147456,
    }),
    __metadata("design:type", Number)
], RealtimeMapDataEventDto.prototype, "data_length", void 0);
class RealtimeStreamStatusDto {
}
exports.RealtimeStreamStatusDto = RealtimeStreamStatusDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Stream session ID',
        example: 'stream_*************_9090',
    }),
    __metadata("design:type", String)
], RealtimeStreamStatusDto.prototype, "session_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current connection status',
        enum: ['connected', 'disconnected', 'connecting', 'error'],
        example: 'connected',
    }),
    __metadata("design:type", String)
], RealtimeStreamStatusDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'IP address being streamed',
        example: '*************',
    }),
    __metadata("design:type", String)
], RealtimeStreamStatusDto.prototype, "ip_address", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Port being used',
        example: 9090,
    }),
    __metadata("design:type", Number)
], RealtimeStreamStatusDto.prototype, "port", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Whether robot data is included',
        example: true,
    }),
    __metadata("design:type", Boolean)
], RealtimeStreamStatusDto.prototype, "include_robot_data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Whether map data is included',
        example: true,
    }),
    __metadata("design:type", Boolean)
], RealtimeStreamStatusDto.prototype, "include_map_data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current update frequency in Hz',
        example: 2,
    }),
    __metadata("design:type", Number)
], RealtimeStreamStatusDto.prototype, "update_frequency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Stream start time',
        example: '2025-07-30T10:30:00.000Z',
    }),
    __metadata("design:type", String)
], RealtimeStreamStatusDto.prototype, "started_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Last data received timestamp',
        example: '2025-07-30T10:30:15.000Z',
        required: false,
    }),
    __metadata("design:type", String)
], RealtimeStreamStatusDto.prototype, "last_data_received", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total messages received',
        example: 150,
    }),
    __metadata("design:type", Number)
], RealtimeStreamStatusDto.prototype, "messages_received", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of connected clients',
        example: 3,
    }),
    __metadata("design:type", Number)
], RealtimeStreamStatusDto.prototype, "connected_clients", void 0);
//# sourceMappingURL=realtime-data.dto.js.map