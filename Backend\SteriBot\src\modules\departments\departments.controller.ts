import { Controller, Get, Post, Patch, Param, Delete, UseGuards, Body } from "@nestjs/common"
import { ApiTags, ApiOperation, ApiBearerAuth } from "@nestjs/swagger"
import { DepartmentsService } from "./departments.service"
import { CreateDepartmentDto } from "./dto/create-department.dto"
import { UpdateDepartmentDto } from "./dto/update-department.dto"
import { FirebaseAuthGuard } from "../../common/guards/firebase-auth.guard"
import { RolesGuard } from "../../common/guards/roles.guard"
import { Roles, UserRole } from "../../common/decorators/roles.decorator"

@ApiTags("Departments")
@Controller("departments")
@UseGuards(FirebaseAuthGuard, RolesGuard)
@ApiBearerAuth()
export class DepartmentsController {
  constructor(private readonly departmentsService: DepartmentsService) {}

  @Post()
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Create a new department (Admin only)" })
  create(@Body() createDepartmentDto: CreateDepartmentDto) {
    return this.departmentsService.create(createDepartmentDto)
  }

  @Get()
  @ApiOperation({ summary: "Get all departments" })
  findAll() {
    return this.departmentsService.findAll()
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get department by ID' })
  findOne(@Param('id') id: string) {
    return this.departmentsService.findById(id)
  }

  @Get(':id/children')
  @ApiOperation({ summary: 'Get child departments' })
  findChildren(@Param('id') id: string) {
    return this.departmentsService.findChildren(id)
  }

  @Patch(":id")
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Update department (Admin only)" })
  update(@Param('id') id: string, @Body() updateDepartmentDto: UpdateDepartmentDto) {
    return this.departmentsService.update(id, updateDepartmentDto)
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Delete department (Admin only)' })
  remove(@Param('id') id: string) {
    return this.departmentsService.remove(id)
  }
}
