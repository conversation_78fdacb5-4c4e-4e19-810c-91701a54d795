import { Injectable, NotFoundException } from "@nestjs/common"
import { FirebaseService } from "../../config/firebase/firebase.service"
import type { CreateZoneDto } from "./dto/create-zone.dto"
import type { UpdateZoneDto } from "./dto/update-zone.dto"

@Injectable()
export class ZonesService {
  private readonly collection = "zones"

  constructor(private firebaseService: FirebaseService) {}

  async create(createZoneDto: CreateZoneDto) {
    const firestore = this.firebaseService.getFirestore()
    const docRef = firestore.collection(this.collection).doc()

    const zoneData = {
      ...createZoneDto,
      zoneId: docRef.id,
      createdAt: new Date(),
    }

    await docRef.set(zoneData)
    return zoneData
  }

  async findAll() {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore.collection(this.collection).get()
    return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
  }

  async findByRoom(roomId: string) {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore.collection(this.collection).where("roomId", "==", roomId).get()

    return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
  }

  async findById(id: string) {
    const firestore = this.firebaseService.getFirestore()
    const doc = await firestore.collection(this.collection).doc(id).get()

    if (!doc.exists) {
      throw new NotFoundException(`Zone with ID ${id} not found`)
    }

    return { id: doc.id, ...doc.data() }
  }

  async update(id: string, updateZoneDto: UpdateZoneDto) {
    const firestore = this.firebaseService.getFirestore()
    const docRef = firestore.collection(this.collection).doc(id)

    await docRef.update({
      ...updateZoneDto,
      updatedAt: new Date(),
    })

    return this.findById(id)
  }

  async markAsDisinfected(id: string) {
    const firestore = this.firebaseService.getFirestore()
    const docRef = firestore.collection(this.collection).doc(id)

    await docRef.update({
      lastDisinfected: new Date(),
      updatedAt: new Date(),
    })

    return this.findById(id)
  }

  async remove(id: string) {
    const firestore = this.firebaseService.getFirestore()
    await firestore.collection(this.collection).doc(id).delete()
    return { message: "Zone deleted successfully" }
  }
}
