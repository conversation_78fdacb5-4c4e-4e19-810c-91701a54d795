import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ROS2ConnectionService, ROS2ConnectionResponse } from './ros2-connection.service';

@ApiTags('ROS2')
@Controller('api')
export class ROS2ConnectionController {
  constructor(private readonly ros2: ROS2ConnectionService) {}

  @Get('connect-ros2')
  @ApiOperation({ summary: 'Test ROS2 WebSocket connections' })
  @ApiResponse({
    status: 200,
    schema: {
      type: 'object',
      properties: {
        ips: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              ip: { type: 'string' },
              status: { type: 'string', enum: ['connected', 'failed'] }
            }
          }
        }
      }
    }
  })
  async connect(): Promise<ROS2ConnectionResponse> {
    return this.ros2.connectToROS2IPs();
  }

  @Get('ros2-health')
  @ApiOperation({ summary: 'ROS2 service health check' })
  @ApiResponse({
    status: 200,
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string' },
        scan_script_exists: { type: 'boolean' }
      }
    }
  })
  healthCheck() {
    return this.ros2.healthCheck();
  }
}