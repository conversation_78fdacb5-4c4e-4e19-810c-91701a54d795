import { IsString, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON><PERSON> } from "class-validator"
import { ApiProperty } from "@nestjs/swagger"

export class CreateJobTitleDto {
  @ApiProperty({
    description: 'Job title name',
    example: 'Software Engineer',
    minLength: 2
  })
  @IsString({ message: 'Job title name must be a string' })
  @MinLength(2, { message: 'Job title name must be at least 2 characters long' })
  name: string

  @ApiProperty({
    description: 'Job title description',
    example: 'Responsible for developing and maintaining software applications',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Description must be a string' })
  description?: string

  @ApiProperty({
    description: 'Department ID that this job title belongs to',
    example: 'dept_123'
  })
  @IsString({ message: 'Department ID must be a string' })
  departmentId: string

  @ApiProperty({
    description: 'Job level or seniority',
    example: 'Senior',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Job level must be a string' })
  level?: string
}
