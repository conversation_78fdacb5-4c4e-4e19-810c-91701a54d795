import { Response } from 'express';
import { RobotsService } from "./robots.service";
import { CreateRobotDto } from "./dto/create-robot.dto";
import { UpdateRobotDto } from "./dto/update-robot.dto";
import { GetRobotDataDto, RobotDataResponseDto } from "./dto/get-robot-data.dto";
import { GetMapDataDto, MapDataResponseDto } from "./dto/get-map-data.dto";
import { StartRealtimeStreamDto, StopRealtimeStreamDto, RealtimeStreamStatusDto } from "./dto/realtime-data.dto";
import { ExportMapImageDto, ExportMapImageResponseDto, BatchExportMapDto, BatchExportResponseDto } from "./dto/map-export.dto";
import { RealtimeDataService } from "./services/realtime-data.service";
import { MapExportService } from "./services/map-export.service";
export declare class RobotsController {
    private readonly robotsService;
    private readonly realtimeDataService;
    private readonly mapExportService;
    private readonly logger;
    constructor(robotsService: RobotsService, realtimeDataService: RealtimeDataService, mapExportService: MapExportService);
    create(createRobotDto: CreateRobotDto): Promise<{
        robotId: string;
        isConnected: boolean;
        createdAt: Date;
        robotName: string;
        serialNumber: string;
        batteryLevel?: number;
        currentPosition?: string;
        ipAddress?: string;
        firmwareVersion?: string;
        maintenanceDate?: string;
        currentTaskId?: string;
    }>;
    findAll(): Promise<{
        id: string;
    }[]>;
    findOne(id: string): Promise<{
        id: string;
    }>;
    update(id: string, updateRobotDto: UpdateRobotDto): Promise<{
        id: string;
    }>;
    updateStatus(id: string, status: any): Promise<{
        id: string;
    }>;
    remove(id: string): Promise<{
        message: string;
    }>;
    retrieveRobotData(request: GetRobotDataDto): Promise<RobotDataResponseDto>;
    retrieveMapData(request: GetMapDataDto): Promise<MapDataResponseDto>;
    robotDataHealthCheck(): Promise<{
        status: string;
        python_script_exists: boolean;
        timestamp: string;
    }>;
    getActiveStreams(): Promise<RealtimeStreamStatusDto[]>;
    startRealtimeStream(request: StartRealtimeStreamDto): Promise<{
        success: boolean;
        session_id: string;
        message: string;
        websocket_info: {
            namespace: string;
            events: string[];
            instructions: string;
        };
    }>;
    stopRealtimeStream(request: StopRealtimeStreamDto): Promise<{
        success: boolean;
        session_id: string;
        message: string;
    }>;
    exportMapImage(request: ExportMapImageDto): Promise<ExportMapImageResponseDto>;
    batchExportMaps(request: BatchExportMapDto): Promise<BatchExportResponseDto>;
    getMapExportHistory(): Promise<import("./dto/map-export.dto").MapExportHistoryDto[]>;
    mapExportHealthCheck(): Promise<{
        status: string;
        python_script_exists: boolean;
        export_directory_exists: boolean;
    }>;
    testROS2Connection(): Promise<{
        success: boolean;
        connection_test: {
            status: string;
            has_position: boolean;
            has_battery: boolean;
            last_updated: string;
        };
        demo_stream: {
            success: any;
            session_id: string;
            error: any;
        };
        websocket_info: {
            namespace: string;
            url: string;
            events: string[];
            instructions: string;
        };
        next_steps: string[];
    }>;
    saveCurrentMap(): Promise<{
        success: boolean;
        filename: string;
        download_url: string;
        file_size: number;
        timestamp: string;
        message: string;
    }>;
    downloadMapFile(filename: string, res: Response): Promise<void>;
}
