import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsNumber, IsIP, IsEnum, IsBoolean, Min, Max } from 'class-validator';

export enum ImageFormat {
  PNG = 'png',
  JPEG = 'jpeg',
  JPG = 'jpg'
}

export enum ColorScheme {
  GRAYSCALE = 'grayscale',
  COLORED = 'colored',
  HIGH_CONTRAST = 'high_contrast',
  CUSTOM = 'custom'
}

export class ExportMapImageDto {
  @ApiProperty({
    description: 'IP address of the ROS2 bridge to get map data from',
    example: '*************',
  })
  @IsString()
  @IsIP()
  ip_address: string;

  @ApiProperty({
    description: 'Port of the ROS2 bridge (optional, defaults to 9090)',
    example: 9090,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  port?: number;

  @ApiProperty({
    description: 'Output image format',
    enum: ImageFormat,
    example: ImageFormat.PNG,
    required: false,
  })
  @IsOptional()
  @IsEnum(ImageFormat)
  format?: ImageFormat;

  @ApiProperty({
    description: 'Color scheme for the map visualization',
    enum: ColorScheme,
    example: ColorScheme.COLORED,
    required: false,
  })
  @IsOptional()
  @IsEnum(ColorScheme)
  color_scheme?: ColorScheme;

  @ApiProperty({
    description: 'Scale factor for image size (1.0 = original resolution)',
    example: 2.0,
    minimum: 0.1,
    maximum: 10.0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0.1)
  @Max(10.0)
  scale_factor?: number;

  @ApiProperty({
    description: 'JPEG quality (only for JPEG format, 1-100)',
    example: 90,
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  jpeg_quality?: number;

  @ApiProperty({
    description: 'Include grid lines in the exported image',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  include_grid?: boolean;

  @ApiProperty({
    description: 'Include robot position marker if available',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  include_robot_position?: boolean;

  @ApiProperty({
    description: 'Custom filename (without extension)',
    example: 'robot_map_2025_07_30',
    required: false,
  })
  @IsOptional()
  @IsString()
  filename?: string;

  @ApiProperty({
    description: 'Add timestamp to filename',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  add_timestamp?: boolean;
}

export class ExportMapImageResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Generated filename',
    example: 'robot_map_2025_07_30_10_30_15.png',
  })
  filename: string;

  @ApiProperty({
    description: 'File path relative to export directory',
    example: 'exports/maps/robot_map_2025_07_30_10_30_15.png',
  })
  file_path: string;

  @ApiProperty({
    description: 'File size in bytes',
    example: 245760,
  })
  file_size: number;

  @ApiProperty({
    description: 'Image dimensions',
  })
  dimensions: {
    width: number;
    height: number;
  };

  @ApiProperty({
    description: 'Map metadata used for export',
  })
  map_info: {
    resolution: number;
    width: number;
    height: number;
    origin: {
      x: number;
      y: number;
    };
  };

  @ApiProperty({
    description: 'Export timestamp',
    example: '2025-07-30T10:30:15.000Z',
  })
  exported_at: string;

  @ApiProperty({
    description: 'Download URL for the exported file',
    example: '/api/robots/map/download/robot_map_2025_07_30_10_30_15.png',
  })
  download_url: string;
}

export class MapExportConfigDto {
  @ApiProperty({
    description: 'Color configuration for different map cell types',
  })
  colors?: {
    free_space: string;      // Hex color for free space (e.g., "#FFFFFF")
    occupied_space: string;  // Hex color for occupied space (e.g., "#000000")
    unknown_space: string;   // Hex color for unknown space (e.g., "#808080")
    robot_position: string;  // Hex color for robot marker (e.g., "#FF0000")
    grid_lines: string;      // Hex color for grid lines (e.g., "#CCCCCC")
  };

  @ApiProperty({
    description: 'Robot position marker configuration',
  })
  robot_marker?: {
    size: number;           // Marker size in pixels
    shape: 'circle' | 'square' | 'arrow';
    show_orientation: boolean;
  };

  @ApiProperty({
    description: 'Grid configuration',
  })
  grid?: {
    spacing: number;        // Grid spacing in map cells
    line_width: number;     // Grid line width in pixels
    opacity: number;        // Grid opacity (0.0 - 1.0)
  };
}

export class BatchExportMapDto {
  @ApiProperty({
    description: 'List of IP addresses to export maps from',
    type: [String],
    example: ['*************', '*************'],
  })
  ip_addresses: string[];

  @ApiProperty({
    description: 'Port for all connections (optional, defaults to 8765)',
    example: 8765,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  port?: number;

  @ApiProperty({
    description: 'Export configuration to apply to all maps',
    type: ExportMapImageDto,
    required: false,
  })
  @IsOptional()
  export_config?: Omit<ExportMapImageDto, 'ip_address' | 'port'>;

  @ApiProperty({
    description: 'Create a ZIP archive of all exported maps',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  create_archive?: boolean;
}

export class BatchExportResponseDto {
  @ApiProperty({
    description: 'Overall success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Number of maps successfully exported',
    example: 2,
  })
  exported_count: number;

  @ApiProperty({
    description: 'Number of maps that failed to export',
    example: 0,
  })
  failed_count: number;

  @ApiProperty({
    description: 'Individual export results',
    type: [ExportMapImageResponseDto],
  })
  results: (ExportMapImageResponseDto & { ip_address: string })[];

  @ApiProperty({
    description: 'Archive information if created',
  })
  archive?: {
    filename: string;
    file_path: string;
    file_size: number;
    download_url: string;
  };

  @ApiProperty({
    description: 'Batch export timestamp',
    example: '2025-07-30T10:30:15.000Z',
  })
  exported_at: string;
}

export class MapExportHistoryDto {
  @ApiProperty({
    description: 'Export ID',
    example: 'export_1722336615000',
  })
  export_id: string;

  @ApiProperty({
    description: 'IP address that was exported',
    example: '*************',
  })
  ip_address: string;

  @ApiProperty({
    description: 'Export filename',
    example: 'robot_map_2025_07_30_10_30_15.png',
  })
  filename: string;

  @ApiProperty({
    description: 'Export format used',
    enum: ImageFormat,
    example: ImageFormat.PNG,
  })
  format: ImageFormat;

  @ApiProperty({
    description: 'File size in bytes',
    example: 245760,
  })
  file_size: number;

  @ApiProperty({
    description: 'Export timestamp',
    example: '2025-07-30T10:30:15.000Z',
  })
  exported_at: string;

  @ApiProperty({
    description: 'Whether file still exists',
    example: true,
  })
  file_exists: boolean;

  @ApiProperty({
    description: 'Download URL if file exists',
    example: '/api/robots/map/download/robot_map_2025_07_30_10_30_15.png',
    required: false,
  })
  download_url?: string;
}
