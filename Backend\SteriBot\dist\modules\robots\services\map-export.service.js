"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var MapExportService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MapExportService = void 0;
const common_1 = require("@nestjs/common");
const child_process_1 = require("child_process");
const path = require("path");
const fs = require("fs");
const map_export_dto_1 = require("../dto/map-export.dto");
let MapExportService = MapExportService_1 = class MapExportService {
    constructor() {
        this.logger = new common_1.Logger(MapExportService_1.name);
        this.maxExportHistory = 100;
        this.pythonScriptPath = path.join(process.cwd(), 'Ros_api', 'map_exporter.py');
        this.exportDirectory = path.join(process.cwd(), 'exports', 'maps');
        this.ensureExportDirectory();
    }
    ensureExportDirectory() {
        try {
            if (!fs.existsSync(this.exportDirectory)) {
                fs.mkdirSync(this.exportDirectory, { recursive: true });
                this.logger.log(`📁 Created export directory: ${this.exportDirectory}`);
            }
        }
        catch (error) {
            this.logger.error(`Failed to create export directory: ${error.message}`);
        }
    }
    async exportMapImage(request) {
        try {
            this.logger.log(`🗺️ Exporting map from ${request.ip_address}:${request.port || 9090}`);
            const timestamp = request.add_timestamp !== false ? `_${new Date().toISOString().replace(/[:.]/g, '_').slice(0, -5)}` : '';
            const baseFilename = request.filename || `robot_map_${request.ip_address.replace(/\./g, '_')}`;
            const format = request.format || map_export_dto_1.ImageFormat.PNG;
            const filename = `${baseFilename}${timestamp}.${format}`;
            const outputPath = path.join(this.exportDirectory, filename);
            const args = [
                this.pythonScriptPath,
                '--ip', request.ip_address,
                '--port', (request.port || 9090).toString(),
                '--output', outputPath,
                '--format', format,
                '--color-scheme', request.color_scheme || map_export_dto_1.ColorScheme.COLORED,
                '--scale', (request.scale_factor || 1.0).toString(),
                '--timeout', '45',
            ];
            if (request.jpeg_quality && format === map_export_dto_1.ImageFormat.JPEG) {
                args.push('--quality', request.jpeg_quality.toString());
            }
            if (request.include_grid) {
                args.push('--grid');
            }
            if (request.include_robot_position) {
                args.push('--robot-pos');
            }
            this.logger.log(`🐍 Executing: python ${args.join(' ')}`);
            const result = await this.executePythonScript(args);
            if (result.success) {
                const stats = fs.statSync(outputPath);
                const response = {
                    success: true,
                    filename: filename,
                    file_path: path.relative(process.cwd(), outputPath),
                    file_size: stats.size,
                    dimensions: result.dimensions || { width: 0, height: 0 },
                    map_info: {
                        resolution: 0.05,
                        width: result.dimensions?.width || 0,
                        height: result.dimensions?.height || 0,
                        origin: { x: 0, y: 0 },
                    },
                    exported_at: new Date().toISOString(),
                    download_url: `/api/robots/map/download/${filename}`,
                };
                this.logger.log(`✅ Map exported successfully: ${filename} (${stats.size} bytes)`);
                return response;
            }
            else {
                throw new Error(result.error || 'Unknown export error');
            }
        }
        catch (error) {
            this.logger.error(`❌ Map export failed: ${error.message}`);
            throw error;
        }
    }
    async batchExportMaps(request) {
        try {
            this.logger.log(`🗺️ Batch exporting maps from ${request.ip_addresses.length} robots`);
            const results = [];
            let exportedCount = 0;
            let failedCount = 0;
            for (const ip_address of request.ip_addresses) {
                try {
                    const exportRequest = {
                        ip_address,
                        port: request.port,
                        ...request.export_config,
                    };
                    const result = await this.exportMapImage(exportRequest);
                    results.push({ ...result, ip_address });
                    exportedCount++;
                }
                catch (error) {
                    this.logger.error(`Failed to export map from ${ip_address}: ${error.message}`);
                    results.push({
                        success: false,
                        filename: '',
                        file_path: '',
                        file_size: 0,
                        dimensions: { width: 0, height: 0 },
                        map_info: { resolution: 0, width: 0, height: 0, origin: { x: 0, y: 0 } },
                        exported_at: new Date().toISOString(),
                        download_url: '',
                        ip_address,
                    });
                    failedCount++;
                }
            }
            let archive = undefined;
            if (request.create_archive && exportedCount > 0) {
                archive = await this.createArchive(results.filter(r => r.success));
            }
            const response = {
                success: exportedCount > 0,
                exported_count: exportedCount,
                failed_count: failedCount,
                results,
                archive,
                exported_at: new Date().toISOString(),
            };
            this.logger.log(`✅ Batch export completed: ${exportedCount} success, ${failedCount} failed`);
            return response;
        }
        catch (error) {
            this.logger.error(`❌ Batch export failed: ${error.message}`);
            throw error;
        }
    }
    async getExportHistory() {
        try {
            const files = fs.readdirSync(this.exportDirectory);
            const history = [];
            for (const filename of files) {
                if (filename.match(/\.(png|jpg|jpeg)$/i)) {
                    const filePath = path.join(this.exportDirectory, filename);
                    const stats = fs.statSync(filePath);
                    const ipMatch = filename.match(/(\d+_\d+_\d+_\d+)/);
                    const ip_address = ipMatch ? ipMatch[1].replace(/_/g, '.') : 'unknown';
                    const format = filename.toLowerCase().endsWith('.png') ? map_export_dto_1.ImageFormat.PNG :
                        filename.toLowerCase().endsWith('.jpg') || filename.toLowerCase().endsWith('.jpeg') ? map_export_dto_1.ImageFormat.JPEG : map_export_dto_1.ImageFormat.PNG;
                    history.push({
                        export_id: `export_${stats.birthtimeMs}`,
                        ip_address,
                        filename,
                        format,
                        file_size: stats.size,
                        exported_at: stats.birthtime.toISOString(),
                        file_exists: true,
                        download_url: `/api/robots/map/download/${filename}`,
                    });
                }
            }
            history.sort((a, b) => new Date(b.exported_at).getTime() - new Date(a.exported_at).getTime());
            return history.slice(0, this.maxExportHistory);
        }
        catch (error) {
            this.logger.error(`Failed to get export history: ${error.message}`);
            return [];
        }
    }
    async getExportFile(filename) {
        const filePath = path.join(this.exportDirectory, filename);
        const exists = fs.existsSync(filePath);
        return { filePath, exists };
    }
    async deleteExportFile(filename) {
        try {
            const filePath = path.join(this.exportDirectory, filename);
            if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
                this.logger.log(`🗑️ Deleted export file: ${filename}`);
                return true;
            }
            return false;
        }
        catch (error) {
            this.logger.error(`Failed to delete export file: ${error.message}`);
            return false;
        }
    }
    async executePythonScript(args) {
        return new Promise((resolve, reject) => {
            const pythonProcess = (0, child_process_1.spawn)('python', args, {
                stdio: ['pipe', 'pipe', 'pipe'],
                cwd: process.cwd(),
            });
            let stdout = '';
            let stderr = '';
            pythonProcess.stdout.on('data', (data) => {
                stdout += data.toString();
            });
            pythonProcess.stderr.on('data', (data) => {
                stderr += data.toString();
            });
            pythonProcess.on('close', (code) => {
                if (code === 0) {
                    try {
                        const result = JSON.parse(stdout.trim());
                        resolve(result);
                    }
                    catch (parseError) {
                        reject(new Error(`Failed to parse Python output: ${parseError.message}`));
                    }
                }
                else {
                    reject(new Error(`Python script failed with code ${code}: ${stderr}`));
                }
            });
            pythonProcess.on('error', (error) => {
                reject(new Error(`Failed to start Python process: ${error.message}`));
            });
            setTimeout(() => {
                if (!pythonProcess.killed) {
                    pythonProcess.kill();
                    reject(new Error('Python script timeout'));
                }
            }, 60000);
        });
    }
    async createArchive(results) {
        const archiveFilename = `map_exports_${new Date().toISOString().replace(/[:.]/g, '_').slice(0, -5)}.zip`;
        return {
            filename: archiveFilename,
            file_path: `exports/archives/${archiveFilename}`,
            file_size: 0,
            download_url: `/api/robots/map/download-archive/${archiveFilename}`,
        };
    }
    async healthCheck() {
        const pythonScriptExists = fs.existsSync(this.pythonScriptPath);
        const exportDirExists = fs.existsSync(this.exportDirectory);
        return {
            status: pythonScriptExists && exportDirExists ? 'healthy' : 'unhealthy',
            python_script_exists: pythonScriptExists,
            export_directory_exists: exportDirExists,
        };
    }
};
exports.MapExportService = MapExportService;
exports.MapExportService = MapExportService = MapExportService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], MapExportService);
//# sourceMappingURL=map-export.service.js.map