export declare enum ImageFormat {
    PNG = "png",
    JPEG = "jpeg",
    JPG = "jpg"
}
export declare enum ColorScheme {
    GRAYSCALE = "grayscale",
    COLORED = "colored",
    HIGH_CONTRAST = "high_contrast",
    CUSTOM = "custom"
}
export declare class ExportMapImageDto {
    ip_address: string;
    port?: number;
    format?: ImageFormat;
    color_scheme?: ColorScheme;
    scale_factor?: number;
    jpeg_quality?: number;
    include_grid?: boolean;
    include_robot_position?: boolean;
    filename?: string;
    add_timestamp?: boolean;
}
export declare class ExportMapImageResponseDto {
    success: boolean;
    filename: string;
    file_path: string;
    file_size: number;
    dimensions: {
        width: number;
        height: number;
    };
    map_info: {
        resolution: number;
        width: number;
        height: number;
        origin: {
            x: number;
            y: number;
        };
    };
    exported_at: string;
    download_url: string;
}
export declare class MapExportConfigDto {
    colors?: {
        free_space: string;
        occupied_space: string;
        unknown_space: string;
        robot_position: string;
        grid_lines: string;
    };
    robot_marker?: {
        size: number;
        shape: 'circle' | 'square' | 'arrow';
        show_orientation: boolean;
    };
    grid?: {
        spacing: number;
        line_width: number;
        opacity: number;
    };
}
export declare class BatchExportMapDto {
    ip_addresses: string[];
    port?: number;
    export_config?: Omit<ExportMapImageDto, 'ip_address' | 'port'>;
    create_archive?: boolean;
}
export declare class BatchExportResponseDto {
    success: boolean;
    exported_count: number;
    failed_count: number;
    results: (ExportMapImageResponseDto & {
        ip_address: string;
    })[];
    archive?: {
        filename: string;
        file_path: string;
        file_size: number;
        download_url: string;
    };
    exported_at: string;
}
export declare class MapExportHistoryDto {
    export_id: string;
    ip_address: string;
    filename: string;
    format: ImageFormat;
    file_size: number;
    exported_at: string;
    file_exists: boolean;
    download_url?: string;
}
