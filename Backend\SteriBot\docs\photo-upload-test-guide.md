# Photo Upload Test Guide

## Overview
Users (both admin and regular users) can only edit their own profile photo. Photos are stored in Firebase Storage with automatic validation and old photo cleanup.

## Prerequisites
1. NestJS server is running
2. Firebase Storage is configured with `FIREBASE_STORAGE_BUCKET` environment variable
3. You have valid user tokens (from login)

## Environment Setup

Add to your `.env` file:
```env
FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com
```

## API Endpoint

```
PATCH /users/{userId}/photo
```

## Test Cases

### 1. Successful Photo Upload (User Updates Own Photo)

```bash
curl -X PATCH \
  http://localhost:3000/users/USER_ID_HERE/photo \
  -H "Authorization: Bearer USER_TOKEN_HERE" \
  -H "Content-Type: multipart/form-data" \
  -F "photo=@/path/to/your/image.jpg"
```

**Expected Response (200):**
```json
{
  "message": "Profile photo updated successfully",
  "photoUrl": "https://storage.googleapis.com/your-bucket/profile-pictures/user123_1234567890.jpg",
  "userId": "user123"
}
```

### 2. Admin Updates Any User's Photo

```bash
curl -X PATCH \
  http://localhost:3000/users/ANY_USER_ID/photo \
  -H "Authorization: Bearer ADMIN_TOKEN_HERE" \
  -H "Content-Type: multipart/form-data" \
  -F "photo=@/path/to/your/image.jpg"
```

**Expected Response (200):** Same as above

### 3. Forbidden - User Tries to Update Another User's Photo

```bash
curl -X PATCH \
  http://localhost:3000/users/DIFFERENT_USER_ID/photo \
  -H "Authorization: Bearer USER_TOKEN_HERE" \
  -H "Content-Type: multipart/form-data" \
  -F "photo=@/path/to/your/image.jpg"
```

**Expected Response (403):**
```json
{
  "statusCode": 403,
  "message": "You can only update your own profile photo",
  "error": "Forbidden"
}
```

### 4. File Validation Tests

#### 4.1 File Too Large (>5MB)
```bash
curl -X PATCH \
  http://localhost:3000/users/USER_ID/photo \
  -H "Authorization: Bearer USER_TOKEN" \
  -F "photo=@/path/to/large-image.jpg"
```

**Expected Response (400):**
```json
{
  "statusCode": 400,
  "message": "File size too large. Maximum allowed size is 5MB",
  "error": "Bad Request"
}
```

#### 4.2 Invalid File Type
```bash
curl -X PATCH \
  http://localhost:3000/users/USER_ID/photo \
  -H "Authorization: Bearer USER_TOKEN" \
  -F "photo=@/path/to/document.pdf"
```

**Expected Response (400):**
```json
{
  "statusCode": 400,
  "message": "Invalid file type. Allowed types: image/jpeg, image/jpg, image/png, image/webp",
  "error": "Bad Request"
}
```

#### 4.3 No File Uploaded
```bash
curl -X PATCH \
  http://localhost:3000/users/USER_ID/photo \
  -H "Authorization: Bearer USER_TOKEN" \
  -H "Content-Type: multipart/form-data"
```

**Expected Response (400):**
```json
{
  "statusCode": 400,
  "message": "No file uploaded",
  "error": "Bad Request"
}
```

### 5. Authentication Tests

#### 5.1 No Token Provided
```bash
curl -X PATCH \
  http://localhost:3000/users/USER_ID/photo \
  -H "Content-Type: multipart/form-data" \
  -F "photo=@/path/to/image.jpg"
```

**Expected Response (401):**
```json
{
  "statusCode": 401,
  "message": "No token provided",
  "error": "Unauthorized"
}
```

#### 5.2 Invalid Token
```bash
curl -X PATCH \
  http://localhost:3000/users/USER_ID/photo \
  -H "Authorization: Bearer invalid_token" \
  -F "photo=@/path/to/image.jpg"
```

**Expected Response (401):**
```json
{
  "statusCode": 401,
  "message": "Invalid token",
  "error": "Unauthorized"
}
```

## File Requirements

### Supported Formats
- JPEG (.jpg, .jpeg)
- PNG (.png)
- WebP (.webp)

### File Size Limit
- Maximum: 5MB

### Validation Rules
1. File must be present
2. File size must be ≤ 5MB
3. MIME type must match allowed image types
4. File extension must match allowed extensions

## Firebase Storage Structure

Photos are stored in Firebase Storage with the following structure:
```
profile-pictures/
├── user123_1234567890.jpg
├── user456_1234567891.png
└── user789_1234567892.webp
```

### File Naming Convention
`{userId}_{timestamp}.{extension}`

## Features

### 1. Automatic Old Photo Cleanup
- When a user uploads a new photo, the old photo is automatically deleted from Firebase Storage
- Only applies to photos stored in Firebase Storage (URLs containing 'storage.googleapis.com')

### 2. Authorization
- Users can only update their own profile photo
- Admins can update any user's profile photo
- Authorization is enforced at the controller level

### 3. File Validation
- Comprehensive validation for file type, size, and format
- Clear error messages for validation failures

### 4. Unique File Names
- Files are renamed with user ID and timestamp to prevent conflicts
- Original filename is not preserved for security

## Testing with Postman

1. **Set up environment variables:**
   - `BASE_URL`: http://localhost:3000
   - `USER_TOKEN`: Your user's JWT token
   - `ADMIN_TOKEN`: Admin user's JWT token
   - `USER_ID`: User ID to test with

2. **Create requests:**
   - Method: PATCH
   - URL: `{{BASE_URL}}/users/{{USER_ID}}/photo`
   - Headers: `Authorization: Bearer {{USER_TOKEN}}`
   - Body: form-data with key `photo` and file value

3. **Test scenarios:**
   - Upload valid image as user (own photo)
   - Upload valid image as admin (any user's photo)
   - Try to upload another user's photo as regular user
   - Upload invalid file types
   - Upload oversized files

## Verification

After successful upload:

1. **Check profile response:**
```bash
GET /auth/profile
Authorization: Bearer USER_TOKEN
```

The response should include the new `picture` URL.

2. **Verify file in Firebase Storage:**
   - Check Firebase Console > Storage
   - Look for file in `profile-pictures/` folder
   - Verify old photo was deleted (if applicable)

3. **Test image accessibility:**
   - Copy the returned `photoUrl`
   - Open in browser to verify image loads correctly
