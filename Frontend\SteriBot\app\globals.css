@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 231 44% 9%;
      --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --primary-gradient: linear-gradient(90deg, #0A3F4C, #0C6980);
  }
  .dark {
    --primary-gradient: linear-gradient(90deg, #0A3F4C, #0C6980);
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 231 44% 9%;
      --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
@layer utilities {
  .bg-sidebar-gradient {
    background: linear-gradient(90deg, #0A3F4C, #0C6980);
  }
}

/* Custom Range Slider Styles */
.range-teal::-webkit-slider-thumb {
  appearance: none;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: linear-gradient(90deg, #14b8a6, #0C6980);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.range-teal::-moz-range-thumb {
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: linear-gradient(90deg, #14b8a6, #0C6980);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.range-teal::-webkit-slider-track {
  height: 8px;
  border-radius: 4px;
}

.range-teal::-moz-range-track {
  height: 8px;
  border-radius: 4px;
}

/* Animated Connect Button */
.connect-button {
  position: relative;
  overflow: hidden;
}

.connect-button::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #14b8a6, #0C6980, #14b8a6, #0C6980);
  background-size: 400% 400%;
  border-radius: 10px;
  z-index: -1;
  animation: gradientShift 3s ease infinite;
  opacity: 0.8;
}

.connect-button:hover::before {
  opacity: 1;
  animation-duration: 1.5s;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Fade in animation for modals */
.modal-fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Connecting Interface Animations */
.wifi-pulse {
  animation: wifiPulse 2s ease-in-out infinite;
}

@keyframes wifiPulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(20, 184, 166, 0.7);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 0 20px rgba(20, 184, 166, 0);
    transform: scale(1.05);
  }
}

.robot-waves {
  position: relative;
}

.robot-waves::before,
.robot-waves::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2px solid rgba(20, 184, 166, 0.3);
  border-radius: 50%;
  animation: robotWaves 3s ease-out infinite;
}

.robot-waves::before {
  width: 120%;
  height: 120%;
  animation-delay: 0s;
}

.robot-waves::after {
  width: 140%;
  height: 140%;
  animation-delay: 1s;
}

@keyframes robotWaves {
  0% {
    width: 100%;
    height: 100%;
    opacity: 1;
  }
  100% {
    width: 200%;
    height: 200%;
    opacity: 0;
  }
}

.flow-arrows {
  animation: flowArrows 2s ease-in-out infinite;
}

@keyframes flowArrows {
  0%, 100% {
    transform: translateX(0);
    opacity: 1;
  }
  50% {
    transform: translateX(10px);
    opacity: 0.7;
  }
}

.gradient-text {
  background: linear-gradient(90deg, #0A3F4C, #0C6980, #14b8a6);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientTextShift 3s ease infinite;
}

@keyframes gradientTextShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.connecting-container {
  background: linear-gradient(135deg, #0A3F4C 0%, #0C6980 50%, #14b8a6 100%);
  background-size: 400% 400%;
  animation: backgroundShift 8s ease infinite;
}

@keyframes backgroundShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Robot Interface Animations */
.robot-interface-container {
  background: linear-gradient(135deg, #0A3F4C 0%, #0C6980 50%, #14b8a6 100%);
  background-size: 400% 400%;
  animation: backgroundShift 8s ease infinite;
}

.robot-glow {
  box-shadow: 0 0 30px rgba(20, 184, 166, 0.3);
}

.start-scan-button {
  box-shadow: 0 10px 30px rgba(20, 184, 166, 0.2);
}

.start-scan-button:hover {
  box-shadow: 0 15px 40px rgba(20, 184, 166, 0.4);
}

.animate-gradient-x {
  background-size: 200% 200%;
  animation: gradient-x 2s ease infinite;
}

@keyframes gradient-x {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Login and Connecting Interface Enhancements */
.login-container {
  background: linear-gradient(135deg, #0A3F4C 0%, #0C6980 50%, #14b8a6 100%);
  background-size: 400% 400%;
  animation: backgroundShift 8s ease infinite;
}

.login-background {
  background: linear-gradient(135deg, #0A3F4C 0%, #0C6980 50%, #14b8a6 100%);
  background-size: 400% 400%;
  animation: backgroundShift 8s ease infinite;
  backdrop-filter: blur(10px);
}

.connecting-container-inverted {
  background: rgba(240, 253, 250, 0.95);
  backdrop-filter: blur(10px);
}

.connecting-background {
  background: linear-gradient(135deg, #0A3F4C 0%, #0C6980 50%, #14b8a6 100%);
  background-size: 400% 400%;
  animation: backgroundShift 8s ease infinite;
  backdrop-filter: blur(15px);
}

/* 3D Effects */
.shadow-3d {
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 15px 25px rgba(20, 184, 166, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.login-modal-3d {
  transform: perspective(1000px) rotateX(2deg) rotateY(-2deg);
  transition: transform 0.3s ease;
}

.login-modal-3d:hover {
  transform: perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1.02);
}

.network-modal-3d {
  transform: perspective(1000px) rotateX(-2deg) rotateY(2deg);
  transition: transform 0.3s ease;
}

.network-modal-3d:hover {
  transform: perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1.02);
}

.connecting-card-3d {
  transform: perspective(1000px) rotateX(1deg);
  transition: transform 0.3s ease;
}

.connecting-card-3d:hover {
  transform: perspective(1000px) rotateX(0deg) scale(1.01);
}