# Firebase Storage Troubleshooting Guide

## Issue Fixed: Bucket Name Not Specified

### Problem
```
FirebaseError: Bucket name not specified or invalid. Specify a valid bucket name via the storageBucket option when initializing the app, or specify the bucket name explicitly when calling the getBucket() method.
```

### Root Cause
The `FIREBASE_STORAGE_BUCKET` environment variable was missing from the `.env` file.

### Solution Applied

1. **Added Missing Environment Variable**
   ```env
   # Added to .env file
   FIREBASE_STORAGE_BUCKET=steribot-23c2c.appspot.com
   ```

2. **Enhanced Error Handling**
   Updated `firebase.service.ts` to:
   - Explicitly specify bucket name when calling `storage.bucket(bucketName)`
   - Provide clear error messages when bucket name is not configured
   - Validate bucket configuration before attempting operations

### Current Firebase Configuration

```env
# Firebase Configuration in .env
FIREBASE_PROJECT_ID=steribot-23c2c
FIREBASE_PRIVATE_KEY_ID=445ca4401f5469f0fcf59c69221d2b1159f3568e
FIREBASE_PRIVATE_KEY="-----B<PERSON>IN PRIVATE KEY-----\n..."
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=103840304075523744397
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40steribot-23c2c.iam.gserviceaccount.com
FIREBASE_DATABASE_URL=https://steribot-23c2c-default-rtdb.firebaseio.com/
FIREBASE_STORAGE_BUCKET=steribot-23c2c.appspot.com  # ✅ ADDED
FIREBASE_WEB_API_KEY=AIzaSyDu-yvXVta9-zlPHFJ7lDq_5DoKT2cOCq4
```

### Testing Firebase Storage

#### 1. Test File Upload via API

**Endpoint**: `POST /api/v1/users/:id/photo`

**Using curl**:
```bash
curl -X POST "http://localhost:3001/api/v1/users/USER_ID/photo" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -F "photo=@/path/to/your/image.jpg"
```

**Using Swagger UI**:
1. Go to http://localhost:3001/api/docs
2. Find the "Users" section
3. Locate `POST /users/{id}/photo`
4. Click "Try it out"
5. Upload a test image file
6. Execute the request

#### 2. Verify Upload in Firebase Console

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `steribot-23c2c`
3. Navigate to **Storage** in the left sidebar
4. Check the `profile-pictures/` folder for uploaded files

#### 3. Test File Deletion

The system automatically deletes old profile pictures when new ones are uploaded.

### Common Firebase Storage Issues

#### Issue 1: Permission Denied
```
Error: Permission denied. Could not perform this operation
```

**Solution**: Ensure Firebase Storage rules allow the operation:
```javascript
// Firebase Storage Rules
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

#### Issue 2: File Size Limits
```
Error: File size exceeds maximum allowed size
```

**Solution**: Check and adjust file size limits in:
1. Firebase Storage rules
2. NestJS multer configuration
3. Frontend file input validation

#### Issue 3: Invalid File Types
```
Error: File type not supported
```

**Solution**: Update allowed file types in the upload validation:
```typescript
// In users.controller.ts
@UseInterceptors(
  FileInterceptor('photo', {
    fileFilter: (req, file, cb) => {
      if (file.mimetype.match(/\/(jpg|jpeg|png|gif)$/)) {
        cb(null, true);
      } else {
        cb(new Error('Only image files are allowed!'), false);
      }
    },
    limits: {
      fileSize: 5 * 1024 * 1024, // 5MB limit
    },
  }),
)
```

### Monitoring and Debugging

#### 1. Server Logs
Monitor the NestJS server logs for Firebase-related errors:
```bash
# Watch server logs
npm run start:dev
```

#### 2. Firebase Admin SDK Logs
Enable detailed logging for Firebase operations:
```typescript
// In firebase.service.ts
console.log('Firebase Storage bucket:', bucketName);
console.log('Uploading file:', filePath);
```

#### 3. Network Connectivity
Ensure the server can reach Firebase services:
```bash
# Test connectivity
ping storage.googleapis.com
```

### File Structure After Fix

```
SteriApp/
├── .env                                     # ✅ Updated with FIREBASE_STORAGE_BUCKET
├── src/config/firebase/
│   └── firebase.service.ts                 # ✅ Enhanced error handling
├── src/modules/users/
│   ├── users.controller.ts                 # File upload endpoint
│   └── users.service.ts                    # File upload logic
└── docs/
    └── FIREBASE_STORAGE_TROUBLESHOOTING.md # This guide
```

### Next Steps

1. **Test the fix**: Try uploading a user profile picture
2. **Monitor logs**: Watch for any remaining Firebase errors
3. **Update documentation**: Keep this guide updated with any new issues
4. **Security review**: Ensure Firebase Storage rules are properly configured

The Firebase Storage configuration should now be working correctly for user profile picture uploads and other file operations.
