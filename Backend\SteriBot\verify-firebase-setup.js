// Script to verify Firebase setup and user creation
const fetch = require('node-fetch');

async function verifyFirebaseSetup() {
  console.log('🔍 Firebase Setup Verification Tool\n');
  console.log('=' .repeat(60));
  
  const baseUrl = 'http://localhost:3001';
  
  // Step 1: Check if server is running
  console.log('\n📡 Step 1: Checking server status...');
  try {
    const response = await fetch(`${baseUrl}/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({})
    });
    console.log('✅ Server is running');
  } catch (error) {
    console.log('❌ Server is not running. Start with: npm run start:dev');
    return;
  }
  
  // Step 2: Test registration
  console.log('\n🔐 Step 2: Testing user registration...');
  
  const timestamp = Date.now();
  const testUser = {
    username: 'testuser',
    email: `verify${timestamp}@example.com`,
    password: 'testpass123',
    role: 'user'
  };
  
  console.log(`Creating user: ${testUser.email}`);
  
  let userId = null;
  
  try {
    const registerResponse = await fetch(`${baseUrl}/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(testUser)
    });
    
    const registerData = await registerResponse.json();
    
    if (registerResponse.ok) {
      console.log('✅ Registration successful');
      console.log(`User ID: ${registerData.userId}`);
      userId = registerData.userId;
    } else {
      console.log('❌ Registration failed');
      console.log('Error:', registerData);
      return;
    }
  } catch (error) {
    console.log('❌ Registration failed:', error.message);
    return;
  }
  
  // Step 3: Wait and test login
  console.log('\n⏳ Step 3: Waiting 2 seconds for Firebase...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  console.log('\n🔑 Step 4: Testing login...');
  
  try {
    const loginResponse = await fetch(`${baseUrl}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password
      })
    });
    
    const loginData = await loginResponse.json();
    
    console.log(`Login Status: ${loginResponse.status}`);
    
    if (loginResponse.ok) {
      console.log('✅ Login successful - Everything is working!');
      console.log('Custom Token received:', loginData.customToken ? 'Yes' : 'No');
    } else {
      console.log('❌ Login failed');
      console.log('Error:', loginData);
      
      // Analyze the specific error
      if (loginData.message?.includes('API key not valid')) {
        console.log('\n🚨 ISSUE IDENTIFIED: Missing or invalid FIREBASE_WEB_API_KEY');
        console.log('\n📋 TO FIX:');
        console.log('1. Go to Firebase Console → Project Settings → General');
        console.log('2. Scroll to "Your apps" section');
        console.log('3. Copy the Web API Key');
        console.log('4. Add to .env file: FIREBASE_WEB_API_KEY=your-key-here');
        console.log('5. Restart the server');
        
      } else if (loginData.message?.includes('EMAIL_NOT_FOUND')) {
        console.log('\n🚨 ISSUE: User not found in Firebase Auth');
        console.log('This means registration didn\'t create the user in Firebase Auth');
        
      } else if (loginData.message?.includes('INVALID_PASSWORD')) {
        console.log('\n🚨 ISSUE: Password verification failed');
        console.log('This means user exists but password is wrong');
        
      } else {
        console.log('\n🚨 UNKNOWN ISSUE:', loginData.message);
      }
    }
  } catch (error) {
    console.log('❌ Login test failed:', error.message);
  }
  
  // Step 5: Test with wrong password
  console.log('\n🧪 Step 5: Testing with wrong password...');
  
  try {
    const wrongPasswordResponse = await fetch(`${baseUrl}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: testUser.email,
        password: 'wrongpassword'
      })
    });
    
    const wrongPasswordData = await wrongPasswordResponse.json();
    
    if (!wrongPasswordResponse.ok) {
      console.log('✅ Wrong password correctly rejected');
      console.log('Error message:', wrongPasswordData.message);
    } else {
      console.log('❌ Wrong password was accepted - this is a problem!');
    }
  } catch (error) {
    console.log('Wrong password test failed:', error.message);
  }
  
  // Step 6: Environment check
  console.log('\n' + '=' .repeat(60));
  console.log('🔧 ENVIRONMENT CHECKLIST:');
  console.log('');
  console.log('Required .env variables:');
  console.log('✓ FIREBASE_PROJECT_ID');
  console.log('✓ FIREBASE_PRIVATE_KEY_ID');
  console.log('✓ FIREBASE_PRIVATE_KEY');
  console.log('✓ FIREBASE_CLIENT_EMAIL');
  console.log('✓ FIREBASE_CLIENT_ID');
  console.log('✓ FIREBASE_CLIENT_X509_CERT_URL');
  console.log('✓ FIREBASE_DATABASE_URL');
  console.log('❗ FIREBASE_WEB_API_KEY (REQUIRED FOR LOGIN)');
  console.log('');
  console.log('Firebase Console Checklist:');
  console.log('✓ Authentication → Sign-in method → Email/Password ENABLED');
  console.log('✓ Project Settings → General → Web app created');
  console.log('✓ Service account has proper permissions');
  console.log('');
  console.log('📝 Note: Password fields are never shown in Firebase Console');
  console.log('   This is normal security behavior - passwords are hashed');
  
  if (userId) {
    console.log('');
    console.log('🔍 To verify user in Firebase Console:');
    console.log('1. Go to Authentication → Users');
    console.log(`2. Look for user with UID: ${userId}`);
    console.log(`3. Email should be: ${testUser.email}`);
    console.log('4. Password field will NOT be visible (this is normal)');
  }
}

// Run verification
verifyFirebaseSetup().catch(console.error);
