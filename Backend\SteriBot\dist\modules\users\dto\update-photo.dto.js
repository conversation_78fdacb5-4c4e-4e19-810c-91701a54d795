"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePhotoResponseDto = exports.UpdatePhotoDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class UpdatePhotoDto {
}
exports.UpdatePhotoDto = UpdatePhotoDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        type: 'string',
        format: 'binary',
        description: 'Profile photo file (JPEG, PNG, WebP)',
        required: true
    }),
    __metadata("design:type", Object)
], UpdatePhotoDto.prototype, "photo", void 0);
class UpdatePhotoResponseDto {
}
exports.UpdatePhotoResponseDto = UpdatePhotoResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'Profile photo updated successfully'
    }),
    __metadata("design:type", String)
], UpdatePhotoResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'New photo URL',
        example: 'https://storage.googleapis.com/your-bucket/profile-pictures/user123_1234567890.jpg'
    }),
    __metadata("design:type", String)
], UpdatePhotoResponseDto.prototype, "photoUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User ID',
        example: 'user123'
    }),
    __metadata("design:type", String)
], UpdatePhotoResponseDto.prototype, "userId", void 0);
//# sourceMappingURL=update-photo.dto.js.map