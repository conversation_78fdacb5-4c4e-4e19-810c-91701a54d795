import { Module } from "@nestjs/common"
import { JobTitlesService } from "./job-titles.service"
import { JobTitlesController } from "./job-titles.controller"
import { FirebaseModule } from "../../config/firebase/firebase.module"
import { DepartmentsModule } from "../departments/departments.module"

@Module({
  imports: [FirebaseModule, DepartmentsModule],
  controllers: [JobTitlesController],
  providers: [JobTitlesService],
  exports: [JobTitlesService],
})
export class JobTitlesModule {}
