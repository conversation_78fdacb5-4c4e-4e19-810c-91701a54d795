import { Module } from "@nestjs/common"
import { AuthController } from "./auth.controller"
import { AuthService } from "./auth.service"
import { UsersModule } from "../users/users.module"
import { DepartmentsModule } from "../departments/departments.module"
import { JobTitlesModule } from "../job-titles/job-titles.module"

@Module({
  imports: [UsersModule, DepartmentsModule, JobTitlesModule],
  controllers: [AuthController],
  providers: [AuthService],
  exports: [AuthService],
})
export class AuthModule {}
