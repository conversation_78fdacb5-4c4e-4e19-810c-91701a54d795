// test-login-integration.js
// Copy and paste this into browser console to test the login integration

console.log('🧪 TESTING LOGIN INTEGRATION...\n');

// Test function to simulate a successful login response
function testLoginIntegration() {
  console.log('📝 Simulating login response...');
  
  // This is the exact format your API should return
  const mockLoginResponse = {
    "message": "Login successful",
    "customToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.EsnKXxbb3W1VPrDshP4LQQxwI-2gTqFmTkNNZ8uQIxpdcXibgd5ehi-UwrmIEfL7OpD4XZMsXcdFYyV0fX3ExRvW7KitdIwuqcBAODoO4kuJba53DCf5pv-CC4xdNc89rBcQGyn3ZgEQZBBEUFbsCWtVkCRjyGqOs3H5foeIjbTxQsDhf05pxqoP6CWwt6K_gTQglEktF6zCwwatMtw3zKcdJuUIdBxrIB4J9g_ZzmvEH9dr6PrIVIf_fCTVxmichkDq_G5FInJuJrg1nYpPuPzVZiytGjpcG-Uu0qE2vAMDxBmaqs20gjqwE4O__dqmKRAhIZx2rNojzx1FDpNyOQ",
    "instructions": "Use this custom token to authenticate with Firebase Auth SDK to get an ID token, or use the test endpoint below",
    "user": {
      "userId": "btNUn3XDVgRtuEWa4K6MzPax7lk2",
      "email": "<EMAIL>",
      "username": "nawel",
      "role": "user"
    }
  };

  console.log('📋 Mock response structure:', mockLoginResponse);

  // Clear localStorage first
  localStorage.removeItem('customToken');
  localStorage.removeItem('userId');
  localStorage.removeItem('userEmail');
  localStorage.removeItem('username');
  localStorage.removeItem('userRole');

  console.log('\n🧹 Cleared localStorage');

  // Test the handleLoginResponse function (if available)
  if (typeof handleLoginResponse === 'function') {
    console.log('✅ handleLoginResponse function found');
    handleLoginResponse(mockLoginResponse);
  } else if (window.handleLoginResponse) {
    console.log('✅ handleLoginResponse found on window');
    window.handleLoginResponse(mockLoginResponse);
  } else {
    console.log('⚠️ handleLoginResponse not found, saving manually...');
    localStorage.setItem('customToken', mockLoginResponse.customToken);
    localStorage.setItem('userId', mockLoginResponse.user.userId);
    localStorage.setItem('userEmail', mockLoginResponse.user.email);
    localStorage.setItem('username', mockLoginResponse.user.username);
    localStorage.setItem('userRole', mockLoginResponse.user.role);
  }

  // Verify the save worked
  console.log('\n🔍 Verification after save:');
  const savedToken = localStorage.getItem('customToken');
  const savedUserId = localStorage.getItem('userId');
  const savedEmail = localStorage.getItem('userEmail');
  const savedUsername = localStorage.getItem('username');
  const savedRole = localStorage.getItem('userRole');

  console.log('customToken:', savedToken ? '✅ SAVED' : '❌ NOT FOUND');
  console.log('userId:', savedUserId ? '✅ SAVED' : '❌ NOT FOUND');
  console.log('userEmail:', savedEmail ? '✅ SAVED' : '❌ NOT FOUND');
  console.log('username:', savedUsername ? '✅ SAVED' : '❌ NOT FOUND');
  console.log('userRole:', savedRole ? '✅ SAVED' : '❌ NOT FOUND');

  if (savedToken && savedUserId) {
    console.log('\n🎉 SUCCESS! Login data integration is working correctly');
    console.log('👤 User ID:', savedUserId);
    console.log('📧 Email:', savedEmail);
    console.log('🏷️ Username:', savedUsername);
    console.log('🔑 Token length:', savedToken.length);
  } else {
    console.log('\n❌ FAILED! Login data was not saved correctly');
  }

  return {
    success: !!(savedToken && savedUserId),
    data: {
      customToken: savedToken,
      userId: savedUserId,
      userEmail: savedEmail,
      username: savedUsername,
      userRole: savedRole
    }
  };
}

// Run the test
console.log('🚀 Running login integration test...\n');
const result = testLoginIntegration();

console.log('\n📊 Test Result:', result.success ? '✅ PASSED' : '❌ FAILED');

// Make test function available globally
window.testLoginIntegration = testLoginIntegration;

console.log('\n🔧 Available functions:');
console.log('  → testLoginIntegration() - Run this test again');
console.log('  → checkLocalStorage() - Check current localStorage status');
