// components/ParametersCard.tsx
import { useState } from "react"

interface Parameters {
  resolution: string
  scanRange: number
  updateRate: number
}

export function ParametersCard() {
  const [parameters, setParameters] = useState<Parameters>({
    resolution: '',
    scanRange: 5,
    updateRate: 15,
  })

  const handleParameterChange = (field: keyof Parameters, value: any) => {
    setParameters((prev) => ({ ...prev, [field]: value }))
  }

  return (
    <div
      className="p-4 rounded-lg text-white"
      style={{ background: 'linear-gradient(90deg, #0A3F4C, #0C6980)' }}
    >
      <h2 className="flex items-center gap-2 font-bold mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="w-5 h-5">
          <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
          <circle cx="12" cy="10" r="3"></circle>
        </svg>
        Parameters
      </h2>

      <div className="space-y-4">
        <div>
          <label className="block text-sm text-gray-200 mb-1">Resolution</label>
          <input
            type="text"
            value={parameters.resolution}
            onChange={(e) => handleParameterChange('resolution', e.target.value)}
            className="w-full p-2 bg-[#0A3F4C] border border-[#0C6980] rounded text-white placeholder-gray-300 focus:border-white focus:outline-none"
          />
        </div>

        <div>
          <label className="block text-sm text-gray-200 mb-1">
            Scan Range: {parameters.scanRange}m
          </label>
          <input
            type="range"
            min="1"
            max="10"
            value={parameters.scanRange}
            onChange={(e) => handleParameterChange('scanRange', parseInt(e.target.value))}
            className="w-full h-2 bg-[#0A3F4C] rounded-lg appearance-none cursor-pointer range-teal"
            style={{
              background: `linear-gradient(to right, #14b8a6 0%, #14b8a6 ${(parameters.scanRange - 1) / 9 * 100}%, #0A3F4C ${(parameters.scanRange - 1) / 9 * 100}%, #0A3F4C 100%)`
            }}
          />
          <div className="flex justify-between text-xs text-gray-300">
            <span>1m</span>
            <span>10m</span>
          </div>
        </div>

        <div>
          <label className="block text-sm text-gray-200 mb-1">
            Update Rate: {parameters.updateRate}Hz
          </label>
          <input
            type="range"
            min="1"
            max="30"
            value={parameters.updateRate}
            onChange={(e) => handleParameterChange('updateRate', parseInt(e.target.value))}
            className="w-full h-2 bg-[#0A3F4C] rounded-lg appearance-none cursor-pointer range-teal"
            style={{
              background: `linear-gradient(to right, #14b8a6 0%, #14b8a6 ${(parameters.updateRate - 1) / 29 * 100}%, #0A3F4C ${(parameters.updateRate - 1) / 29 * 100}%, #0A3F4C 100%)`
            }}
          />
          <div className="flex justify-between text-xs text-gray-300">
            <span>1Hz</span>
            <span>30Hz</span>
          </div>
        </div>
      </div>
    </div>
  )
}