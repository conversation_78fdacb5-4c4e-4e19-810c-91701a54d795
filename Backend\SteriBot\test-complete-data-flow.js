#!/usr/bin/env node
/**
 * Complete Data Flow Test for SteriBot
 * Tests: ROS2 → Backend → WebSocket → Frontend
 * 
 * This script:
 * 1. Tests ROS2 connection
 * 2. Starts real-time stream via API
 * 3. Connects to WebSocket to receive data
 * 4. Tests map export functionality
 */

const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
const WebSocket = require('ws');

const BACKEND_URL = 'http://localhost:3001';
const WEBSOCKET_URL = 'ws://localhost:3001/realtime-robot-data';
const ROS2_IP = '*************';
const ROS2_PORT = 9090;

console.log('🤖 SteriBot Complete Data Flow Test');
console.log('=' .repeat(60));
console.log(`Backend: ${BACKEND_URL}`);
console.log(`WebSocket: ${WEBSOCKET_URL}`);
console.log(`ROS2 Target: ${ROS2_IP}:${ROS2_PORT}`);
console.log('');

class DataFlowTester {
  constructor() {
    this.sessionId = null;
    this.websocket = null;
    this.dataReceived = {
      robot_data: 0,
      map_data: 0,
      connection: 0
    };
  }

  async testBackendHealth() {
    console.log('🏥 Step 1: Testing backend health...');
    try {
      const response = await fetch(`${BACKEND_URL}/api/v1/robots/data/health`);
      const result = await response.json();
      console.log(`   ✅ Backend health: ${result.status}`);
      return result;
    } catch (error) {
      console.log(`   ❌ Backend health check failed: ${error.message}`);
      throw error;
    }
  }

  async testROS2Connection() {
    console.log('🔗 Step 2: Testing ROS2 connection...');
    try {
      const response = await fetch(`${BACKEND_URL}/api/v1/robots/test/connection`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      
      const result = await response.json();
      
      if (result.success) {
        console.log(`   ✅ ROS2 connection: ${result.connection_test.status}`);
        console.log(`   📍 Position data: ${result.connection_test.has_position ? 'Available' : 'Not available'}`);
        console.log(`   🔋 Battery data: ${result.connection_test.has_battery ? 'Available' : 'Not available'}`);
        
        if (result.demo_stream && result.demo_stream.success) {
          console.log(`   🚀 Demo stream started successfully`);
        }
      } else {
        console.log(`   ❌ ROS2 connection failed`);
      }
      
      return result;
    } catch (error) {
      console.log(`   ❌ ROS2 connection test failed: ${error.message}`);
      throw error;
    }
  }

  async startRealtimeStream() {
    console.log('📡 Step 3: Starting real-time stream...');
    try {
      const streamConfig = {
        ip_address: ROS2_IP,
        port: ROS2_PORT,
        include_robot_data: true,
        include_map_data: true,
        update_frequency: 2
      };

      const response = await fetch(`${BACKEND_URL}/api/v1/robots/realtime/start`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(streamConfig)
      });

      const result = await response.json();
      
      if (result.success) {
        this.sessionId = result.session_id;
        console.log(`   ✅ Stream started: ${this.sessionId}`);
        console.log(`   📡 WebSocket namespace: ${result.websocket_info.namespace}`);
      } else {
        console.log(`   ❌ Failed to start stream: ${result.error}`);
      }
      
      return result;
    } catch (error) {
      console.log(`   ❌ Stream start failed: ${error.message}`);
      throw error;
    }
  }

  async testWebSocketConnection() {
    console.log('🔌 Step 4: Testing WebSocket connection...');
    
    return new Promise((resolve, reject) => {
      try {
        this.websocket = new WebSocket(WEBSOCKET_URL);
        let connected = false;
        
        const timeout = setTimeout(() => {
          if (!connected) {
            this.websocket.close();
            reject(new Error('WebSocket connection timeout'));
          }
        }, 10000);

        this.websocket.on('open', () => {
          connected = true;
          clearTimeout(timeout);
          console.log('   ✅ WebSocket connected');
          
          // Listen for data for 15 seconds
          setTimeout(() => {
            this.websocket.close();
            resolve(this.dataReceived);
          }, 15000);
        });

        this.websocket.on('message', (data) => {
          try {
            const message = JSON.parse(data.toString());
            
            if (message.event) {
              this.dataReceived[message.event] = (this.dataReceived[message.event] || 0) + 1;
              
              if (this.dataReceived.robot_data + this.dataReceived.map_data + this.dataReceived.connection <= 5) {
                console.log(`   📨 Received: ${message.event} (${JSON.stringify(message).substring(0, 100)}...)`);
              }
            }
          } catch (e) {
            // Ignore non-JSON messages
          }
        });

        this.websocket.on('error', (error) => {
          clearTimeout(timeout);
          console.log(`   ❌ WebSocket error: ${error.message}`);
          reject(error);
        });

        this.websocket.on('close', () => {
          clearTimeout(timeout);
          if (connected) {
            console.log('   🔌 WebSocket closed gracefully');
          }
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  async testMapExport() {
    console.log('🗺️  Step 5: Testing map export...');
    try {
      const exportConfig = {
        ip_address: ROS2_IP,
        port: ROS2_PORT,
        format: 'png',
        color_scheme: 'colored',
        filename: 'test_map'
      };

      const response = await fetch(`${BACKEND_URL}/api/v1/robots/map/export`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(exportConfig)
      });

      const result = await response.json();
      
      if (result.success) {
        console.log(`   ✅ Map exported: ${result.filename}`);
        console.log(`   📁 File size: ${(result.file_size / 1024).toFixed(1)} KB`);
        console.log(`   📐 Dimensions: ${result.dimensions.width}x${result.dimensions.height}`);
        console.log(`   🔗 Download URL: ${result.download_url}`);
      } else {
        console.log(`   ❌ Map export failed`);
      }
      
      return result;
    } catch (error) {
      console.log(`   ❌ Map export failed: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  async cleanup() {
    console.log('🧹 Step 6: Cleanup...');
    
    if (this.sessionId) {
      try {
        const response = await fetch(`${BACKEND_URL}/api/v1/robots/realtime/stop`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ session_id: this.sessionId })
        });
        
        const result = await response.json();
        if (result.success) {
          console.log(`   ✅ Stream stopped: ${this.sessionId}`);
        }
      } catch (error) {
        console.log(`   ⚠️  Failed to stop stream: ${error.message}`);
      }
    }
    
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.close();
    }
  }

  printSummary(results) {
    console.log('\n' + '=' .repeat(60));
    console.log('📊 TEST SUMMARY');
    console.log('=' .repeat(60));
    
    const tests = [
      { name: 'Backend Health', status: results.health ? '✅ PASS' : '❌ FAIL' },
      { name: 'ROS2 Connection', status: results.ros2 ? '✅ PASS' : '❌ FAIL' },
      { name: 'Real-time Stream', status: results.stream ? '✅ PASS' : '❌ FAIL' },
      { name: 'WebSocket Data', status: results.websocket && (results.websocket.robot_data > 0 || results.websocket.map_data > 0) ? '✅ PASS' : '❌ FAIL' },
      { name: 'Map Export', status: results.mapExport && results.mapExport.success ? '✅ PASS' : '❌ FAIL' }
    ];
    
    tests.forEach(test => {
      console.log(`${test.status} ${test.name}`);
    });
    
    if (results.websocket) {
      console.log('\n📈 Data Received:');
      console.log(`   Robot Data: ${results.websocket.robot_data} messages`);
      console.log(`   Map Data: ${results.websocket.map_data} messages`);
      console.log(`   Connection Events: ${results.websocket.connection} messages`);
    }
    
    const allPassed = tests.every(test => test.status.includes('✅'));
    console.log(`\n🎯 Overall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
    
    if (allPassed) {
      console.log('\n🚀 Ready for Frontend Integration!');
      console.log('Frontend can now:');
      console.log('1. Connect to WebSocket: ws://localhost:3001/realtime-robot-data');
      console.log('2. Listen for robot_data events (position, battery)');
      console.log('3. Listen for map_data events (real-time map)');
      console.log('4. Download maps via: POST /api/v1/robots/map/export');
    }
  }
}

async function main() {
  const tester = new DataFlowTester();
  const results = {};
  
  try {
    results.health = await tester.testBackendHealth();
    results.ros2 = await tester.testROS2Connection();
    results.stream = await tester.startRealtimeStream();
    results.websocket = await tester.testWebSocketConnection();
    results.mapExport = await tester.testMapExport();
    
  } catch (error) {
    console.log(`\n❌ Test failed: ${error.message}`);
  } finally {
    await tester.cleanup();
    tester.printSummary(results);
  }
}

if (require.main === module) {
  main();
}
