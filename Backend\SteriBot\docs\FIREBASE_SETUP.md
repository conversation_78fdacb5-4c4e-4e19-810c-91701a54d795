# Firebase Setup Guide

## Prerequisites
- Firebase project `steribot-23c2c` should already exist
- You need admin access to the Firebase project

## Step 1: Generate Service Account Key

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your `steribot-23c2c` project
3. Click the gear icon (⚙️) → **Project Settings**
4. Navigate to the **Service accounts** tab
5. Click **Generate new private key**
6. Click **Generate key** to download the JSON file
7. Save the file securely (don't commit it to version control)

## Step 2: Extract Credentials from JSON

Your downloaded JSON file will look like this:
```json
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
```

## Step 3: Get Firebase Web API Key (CRITICAL FOR LOGIN)

⚠️ **This step is REQUIRED for login functionality to work!**

1. In your Firebase Console, go to **Project Settings** (gear icon)
2. Navigate to the **General** tab
3. Scroll down to the **Your apps** section
4. If you don't have a web app:
   - Click **Add app** → **Web** (</> icon)
   - Give it a name (e.g., "SteriBot Web")
   - Click **Register app**
5. Copy the **Web API Key** from the Firebase SDK snippet
   - It looks like: `AIzaSyC...` (starts with AIzaSyC)
   - This is different from your service account credentials!

## Step 4: Update .env File

Copy the values from your JSON file and Firebase console to your `.env` file:

```env
# Application Configuration
NODE_ENV=development
PORT=3001

# Firebase Configuration
FIREBASE_PROJECT_ID=steribot-23c2c
FIREBASE_PRIVATE_KEY_ID=abc123...
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nMIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC...\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=*********...
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-xxxxx%40steribot-23c2c.iam.gserviceaccount.com
FIREBASE_DATABASE_URL=https://steribot-23c2c-default-rtdb.firebaseio.com/
FIREBASE_WEB_API_KEY=AIzaSyC...your-web-api-key-here
```

## Important Notes

1. **Private Key Format**: Keep the private key exactly as it appears in the JSON, including `\n` characters
2. **Quotes**: Wrap the private key in double quotes in the .env file
3. **Security**: Never commit the .env file with real credentials to version control
4. **Firestore**: Make sure Firestore Database is enabled in your Firebase project
5. **Authentication**: Ensure Firebase Authentication is enabled if you plan to use user registration
6. **Web API Key**: The Web API Key is required for password verification during login

## Step 5: Enable Required Firebase Services

In your Firebase Console:
1. **Firestore Database**: Go to Firestore Database → Create database
2. **Authentication**: Go to Authentication → Get started → Enable Email/Password provider
3. **Realtime Database** (if needed): Go to Realtime Database → Create database

## Step 6: Test the Connection

After updating your `.env` file:
```bash
npm run build
npm start
```

If successful, you should see:
- All modules loading without errors
- Routes being mapped
- Application listening on port 3001
