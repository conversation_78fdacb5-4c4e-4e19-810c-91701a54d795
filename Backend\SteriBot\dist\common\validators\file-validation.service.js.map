{"version": 3, "file": "file-validation.service.js", "sourceRoot": "", "sources": ["../../../src/common/validators/file-validation.service.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAgE;AAGzD,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IAA3B;QACY,qBAAgB,GAAG;YAClC,YAAY;YACZ,WAAW;YACX,WAAW;YACX,YAAY;SACb,CAAA;QAEgB,gBAAW,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAA;IA+EhD,CAAC;IAxEC,iBAAiB,CAAC,IAAyB;QACzC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,4BAAmB,CAAC,kBAAkB,CAAC,CAAA;QACnD,CAAC;QAGD,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACjC,MAAM,IAAI,4BAAmB,CAC3B,gDAAgD,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CACrF,CAAA;QACH,CAAC;QAGD,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnD,MAAM,IAAI,4BAAmB,CAC3B,qCAAqC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CACxE,CAAA;QACH,CAAC;QAGD,MAAM,iBAAiB,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAA;QAC5D,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAA;QAE5E,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;YAC/C,MAAM,IAAI,4BAAmB,CAC3B,+CAA+C,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC9E,CAAA;QACH,CAAC;IACH,CAAC;IAQD,sBAAsB,CAAC,MAAc,EAAE,YAAoB;QACzD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAC5B,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAA;QACrD,OAAO,GAAG,MAAM,IAAI,SAAS,GAAG,SAAS,EAAE,CAAA;IAC7C,CAAC;IAOO,gBAAgB,CAAC,QAAgB;QACvC,MAAM,YAAY,GAAG,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;QAC9C,OAAO,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;IACpE,CAAC;IAOD,2BAA2B,CAAC,QAAgB;QAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAA;QAE/D,QAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,MAAM,CAAC;YACZ,KAAK,OAAO;gBACV,OAAO,YAAY,CAAA;YACrB,KAAK,MAAM;gBACT,OAAO,WAAW,CAAA;YACpB,KAAK,OAAO;gBACV,OAAO,YAAY,CAAA;YACrB;gBACE,OAAO,0BAA0B,CAAA;QACrC,CAAC;IACH,CAAC;CACF,CAAA;AAvFY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;GACA,qBAAqB,CAuFjC"}