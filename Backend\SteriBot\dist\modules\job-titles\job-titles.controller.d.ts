import { JobTitlesService } from "./job-titles.service";
import { CreateJobTitleDto } from "./dto/create-job-title.dto";
import { UpdateJobTitleDto } from "./dto/update-job-title.dto";
export declare class JobTitlesController {
    private readonly jobTitlesService;
    constructor(jobTitlesService: JobTitlesService);
    create(createJobTitleDto: CreateJobTitleDto): Promise<{
        createdAt: Date;
        updatedAt: Date;
        name: string;
        description?: string;
        departmentId: string;
        level?: string;
        id: string;
    }>;
    findAll(): Promise<any[]>;
    findOne(id: string): Promise<any>;
    findByDepartment(departmentId: string): Promise<{
        id: string;
    }[]>;
    update(id: string, updateJobTitleDto: UpdateJobTitleDto): Promise<any>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
