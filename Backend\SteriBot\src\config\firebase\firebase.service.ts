import { Injectable, type OnModuleInit } from "@nestjs/common"
import { ConfigService } from "@nestjs/config"
import * as admin from "firebase-admin"
import { getFirestore } from "firebase-admin/firestore"
import { getAuth } from "firebase-admin/auth"
import { getStorage } from "firebase-admin/storage"

@Injectable()
export class FirebaseService implements OnModuleInit {
  private firestore: admin.firestore.Firestore
  private auth: admin.auth.Auth
  private storage: admin.storage.Storage

  constructor(private configService: ConfigService) {}

  async onModuleInit() {
    try {
      // Set up proxy agent if needed for corporate networks
      if (process.env.NODE_TLS_REJECT_UNAUTHORIZED === '0') {
        process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0'
      }

      const serviceAccount = {
        type: "service_account",
        project_id: this.configService.get<string>("FIREBASE_PROJECT_ID"),
        private_key_id: this.configService.get<string>("FIREBASE_PRIVATE_KEY_ID"),
        private_key: this.configService.get<string>("FIREBASE_PRIVATE_KEY")?.replace(/\\n/g, "\n"),
        client_email: this.configService.get<string>("FIREBASE_CLIENT_EMAIL"),
        client_id: this.configService.get<string>("FIREBASE_CLIENT_ID"),
        auth_uri: "https://accounts.google.com/o/oauth2/auth",
        token_uri: "https://oauth2.googleapis.com/token",
        auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
        client_x509_cert_url: this.configService.get<string>("FIREBASE_CLIENT_X509_CERT_URL"),
      }

      if (!admin.apps.length) {
        admin.initializeApp({
          credential: admin.credential.cert(serviceAccount as admin.ServiceAccount),
          databaseURL: this.configService.get<string>("FIREBASE_DATABASE_URL"),
          storageBucket: this.configService.get<string>("FIREBASE_STORAGE_BUCKET"),
        })
      }

      this.firestore = getFirestore()
      this.auth = getAuth()
      this.storage = getStorage()

      console.log('Firebase initialized successfully')
    } catch (error) {
      console.error('Firebase initialization error:', error)
      throw error
    }
  }

  getFirestore(): admin.firestore.Firestore {
    return this.firestore
  }

  getAuth(): admin.auth.Auth {
    return this.auth
  }

  getStorage(): admin.storage.Storage {
    return this.storage
  }

  /**
   * Upload a file to Firebase Storage
   * @param buffer - File buffer
   * @param fileName - Name of the file
   * @param contentType - MIME type of the file
   * @param folder - Optional folder path (default: 'profile-pictures')
   * @returns Promise<string> - Download URL of the uploaded file
   */
  async uploadFile(
    buffer: Buffer,
    fileName: string,
    contentType: string,
    folder: string = 'profile-pictures'
  ): Promise<string> {
    try {
      // Get the storage bucket name from config
      const bucketName = this.configService.get<string>("FIREBASE_STORAGE_BUCKET")
      if (!bucketName) {
        throw new Error('Firebase Storage bucket name not configured. Please set FIREBASE_STORAGE_BUCKET in environment variables.')
      }

      const bucket = this.storage.bucket(bucketName)
      const filePath = `${folder}/${fileName}`
      const file = bucket.file(filePath)

      // Upload the file
      await file.save(buffer, {
        metadata: {
          contentType,
        },
        public: true,
      })

      // Get the download URL
      const [url] = await file.getSignedUrl({
        action: 'read',
        expires: '03-09-2491', // Far future date
      })

      return url
    } catch (error) {
      console.error('Error uploading file to Firebase Storage:', error)
      throw new Error('Failed to upload file')
    }
  }

  /**
   * Delete a file from Firebase Storage
   * @param filePath - Path to the file in storage
   */
  async deleteFile(filePath: string): Promise<void> {
    try {
      // Get the storage bucket name from config
      const bucketName = this.configService.get<string>("FIREBASE_STORAGE_BUCKET")
      if (!bucketName) {
        throw new Error('Firebase Storage bucket name not configured. Please set FIREBASE_STORAGE_BUCKET in environment variables.')
      }

      const bucket = this.storage.bucket(bucketName)
      const file = bucket.file(filePath)
      await file.delete()
    } catch (error) {
      console.error('Error deleting file from Firebase Storage:', error)
      throw new Error('Failed to delete file')
    }
  }

  /**
   * Extract file path from Firebase Storage URL
   * @param url - Firebase Storage URL
   * @returns string - File path
   */
  extractFilePathFromUrl(url: string): string {
    try {
      const urlParts = url.split('/')
      const bucketIndex = urlParts.findIndex(part => part.includes('appspot.com'))
      if (bucketIndex !== -1 && bucketIndex + 2 < urlParts.length) {
        return urlParts.slice(bucketIndex + 2).join('/').split('?')[0]
      }
      throw new Error('Invalid Firebase Storage URL')
    } catch (error) {
      console.error('Error extracting file path from URL:', error)
      throw new Error('Invalid Firebase Storage URL')
    }
  }
}
