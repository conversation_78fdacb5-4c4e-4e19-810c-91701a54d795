# Create Additional Job Title Example

## Step 1: First, get existing departments to choose from

```bash
GET /departments
Authorization: Bearer YOUR_ADMIN_TOKEN
```

Expected response:
```json
[
  {
    "id": "dept_001",
    "name": "Engineering",
    "description": "Software development and engineering team",
    "code": "ENG"
  },
  {
    "id": "dept_002", 
    "name": "Operations",
    "description": "Daily operations and maintenance",
    "code": "OPS"
  },
  {
    "id": "dept_003",
    "name": "Quality Assurance", 
    "description": "Quality control and testing",
    "code": "QA"
  }
]
```

## Step 2: Create a new job title - "Senior Software Engineer"

```bash
POST /job-titles
Content-Type: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN

{
  "name": "Senior Software Engineer",
  "description": "Experienced software engineer responsible for leading development projects, mentoring junior developers, and architecting complex software solutions",
  "departmentId": "dept_001"
}
```

Replace `dept_001` with the actual Engineering department ID from Step 1.

## Expected Response:

```json
{
  "id": "jt_004",
  "name": "Senior Software Engineer", 
  "description": "Experienced software engineer responsible for leading development projects, mentoring junior developers, and architecting complex software solutions",
  "departmentId": "dept_001",
  "createdAt": "2025-07-30T10:30:00.000Z",
  "updatedAt": "2025-07-30T10:30:00.000Z"
}
```

## Step 3: Verify the job title was created

```bash
GET /job-titles
Authorization: Bearer YOUR_ADMIN_TOKEN
```

You should see the new "Senior Software Engineer" job title in the response.

## Step 4: Get job titles by department to verify association

```bash
GET /job-titles/department/dept_001
Authorization: Bearer YOUR_ADMIN_TOKEN
```

This should return all job titles associated with the Engineering department, including the new one.

## Alternative Job Title Examples

You can also create these additional job titles:

### DevOps Engineer (Engineering Department)
```json
{
  "name": "DevOps Engineer",
  "description": "Responsible for deployment automation, infrastructure management, and CI/CD pipeline maintenance",
  "departmentId": "ENGINEERING_DEPARTMENT_ID"
}
```

### Operations Supervisor (Operations Department)  
```json
{
  "name": "Operations Supervisor",
  "description": "Supervises daily operations, coordinates with different teams, and ensures operational efficiency",
  "departmentId": "OPERATIONS_DEPARTMENT_ID"
}
```

### Quality Assurance Lead (QA Department)
```json
{
  "name": "Quality Assurance Lead", 
  "description": "Leads the QA team, develops testing strategies, and ensures product quality standards",
  "departmentId": "QA_DEPARTMENT_ID"
}
```

## Job Title Details for Implementation:

**Recommended Job Title to Create:**
- **Name**: "Senior Software Engineer"
- **Description**: "Experienced software engineer responsible for leading development projects, mentoring junior developers, and architecting complex software solutions"
- **Department**: Engineering (use the Engineering department ID from your database)

This job title provides a good example of a senior-level position that would be commonly used in testing user registration and profile functionality.
