// components/DirectionControls.tsx
export function DirectionControls() {
  return (
    <div className="flex justify-end gap-2 mt-4">
      <button
        className="w-10 h-10 p-0 flex items-center justify-center rounded transition-all hover:scale-105"
        style={{ background: 'linear-gradient(90deg, #14b8a6, #0C6980)' }}
      >
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="white">
          <path d="M12 20l-8-8 1.41-1.41L12 17.17l7.59-7.59L20 12l-8 8z" />
        </svg>
      </button>
      <button
        className="w-10 h-10 p-0 flex items-center justify-center rounded transition-all hover:scale-105"
        style={{ background: 'linear-gradient(90deg, #14b8a6, #0C6980)' }}
      >
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="white">
          <path d="M12 4l8 8-1.41 1.41L13 6.41 4.41 14 4 12l8-8z" />
        </svg>
      </button>
      <button
        className="w-10 h-10 p-0 flex items-center justify-center rounded transition-all hover:scale-105"
        style={{ background: 'linear-gradient(90deg, #14b8a6, #0C6980)' }}
      >
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="white">
          <path d="M20 12l-1.41-1.41L13 16.17l-7.59-7.59L4 12l8 8 8-8z" />
        </svg>
      </button>
      <button
        className="w-10 h-10 p-0 flex items-center justify-center rounded transition-all hover:scale-105"
        style={{ background: 'linear-gradient(90deg, #14b8a6, #0C6980)' }}
      >
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="white">
          <path d="M4 12l1.41 1.41L10.83 7.59 12 6.41l1.17 1.17L19 12l-8 8-1.41-1.41L12 15.41 6.41 10 7.59 8.83z" />
        </svg>
      </button>
    </div>
  )
}