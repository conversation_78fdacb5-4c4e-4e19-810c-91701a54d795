"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateRobotDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class CreateRobotDto {
}
exports.CreateRobotDto = CreateRobotDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the robot',
        example: 'SteriBot-01'
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateRobotDto.prototype, "robotName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Serial number of the robot',
        example: 'SB-2024-001'
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateRobotDto.prototype, "serialNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Current battery level (0-100)',
        example: 85
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateRobotDto.prototype, "batteryLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Current position/location of the robot',
        example: 'Charging Station A'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateRobotDto.prototype, "currentPosition", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'IP address of the robot',
        example: '*************'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsIP)(),
    __metadata("design:type", String)
], CreateRobotDto.prototype, "ipAddress", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Firmware version',
        example: 'v2.1.3'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateRobotDto.prototype, "firmwareVersion", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Last maintenance date',
        example: '2024-01-15T10:00:00.000Z'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateRobotDto.prototype, "maintenanceDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'ID of current task being executed',
        example: 'task123'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateRobotDto.prototype, "currentTaskId", void 0);
//# sourceMappingURL=create-robot.dto.js.map