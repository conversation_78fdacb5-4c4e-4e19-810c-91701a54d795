// components/ActionButtons.tsx
import { useState } from 'react';

interface ActionButtonsProps {
  onMapSaved?: () => void;
  onReset?: () => void;
  isMapSavedState?: boolean;
}

export function ActionButtons({ onMapSaved, onReset, isMapSavedState = false }: ActionButtonsProps) {
  const [isScanning, setIsScanning] = useState(true);
  const [isMapSaved, setIsMapSaved] = useState(false);

  const handleScanAction = () => {
    if (isScanning) {
      // Stop the scan
      setIsScanning(false);
    } else {
      // Save the map
      setIsMapSaved(true);
      // Notify parent component that map is saved
      onMapSaved?.();
      // You can add additional logic here to actually save the map
      setTimeout(() => setIsMapSaved(false), 2000); // Reset after 2 seconds
    }
  }

  const resetMap = () => {
    setIsScanning(true);
    setIsMapSaved(false);
    // Notify parent component to reset
    onReset?.();
  }

  return (
    <div className="space-y-2 flex flex-col">
      <button
        onClick={handleScanAction}
        className={`w-full text-white py-2 px-4 rounded flex items-center justify-center gap-2 mt-auto transition-all hover:scale-105 ${isScanning ? '' : ''}`}
        style={{
          background: isScanning
            ? 'linear-gradient(90deg, #dc2626, #b91c1c)'
            : 'linear-gradient(90deg, #14b8a6, #0C6980)'
        }}
      >
        {isScanning ? (
          <>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <circle cx="12" cy="12" r="10" />
              <rect x="9" y="9" width="6" height="6" />
            </svg>
            Stop Scan
          </>
        ) : (
          <>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z" />
              <polyline points="17 21 17 13 7 13 7 21" />
              <polyline points="7 3 7 8 15 8" />
            </svg>
            {isMapSaved ? 'Map Saved!' : 'Save the Map'}
          </>
        )}
      </button>

      <button
        onClick={resetMap}
        className="w-full text-white py-2 px-4 rounded flex items-center justify-center gap-2 transition-all hover:scale-105"
        style={{ background: 'linear-gradient(90deg, #14b8a6, #0C6980)' }}
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" />
          <path d="M21 3v5h-5" />
          <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" />
          <path d="M8 16H3v5" />
        </svg>
        Reset Map
      </button>
    </div>
  )
}