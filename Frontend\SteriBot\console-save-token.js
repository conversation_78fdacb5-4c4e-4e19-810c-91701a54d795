// console-save-token.js
// Copy and paste this directly into your browser console

// ===== STEP 1: CHECK WHAT'S CURRENTLY IN LOCALSTORAGE =====
console.log('🔍 CHECKING CURRENT LOCALSTORAGE STATUS...\n');

function checkLocalStorageStatus() {
  const customToken = localStorage.getItem('customToken');
  const userId = localStorage.getItem('userId');

  console.log('📋 Current Status:');
  console.log('customToken:', customToken ? '✅ FOUND' : '❌ NOT FOUND');
  if (customToken) {
    console.log('  → Length:', customToken.length, 'characters');
    console.log('  → Preview:', customToken.substring(0, 50) + '...');
  }

  console.log('userId:', userId ? '✅ FOUND' : '❌ NOT FOUND');
  if (userId) {
    console.log('  → Value:', userId);
  }

  console.log('\n📦 All localStorage keys:', Object.keys(localStorage));

  return { hasToken: !!customToken, hasUserId: !!userId };
}

// Check current status
const currentStatus = checkLocalStorageStatus();

// ===== STEP 2: SAVE YOUR LOGIN DATA =====
console.log('\n💾 SAVING YOUR LOGIN DATA...\n');

const loginData = {
  customToken: "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.EsnKXxbb3W1VPrDshP4LQQxwI-2gTqFmTkNNZ8uQIxpdcXibgd5ehi-UwrmIEfL7OpD4XZMsXcdFYyV0fX3ExRvW7KitdIwuqcBAODoO4kuJba53DCf5pv-CC4xdNc89rBcQGyn3ZgEQZBBEUFbsCWtVkCRjyGqOs3H5foeIjbTxQsDhf05pxqoP6CWwt6K_gTQglEktF6zCwwatMtw3zKcdJuUIdBxrIB4J9g_ZzmvEH9dr6PrIVIf_fCTVxmichkDq_G5FInJuJrg1nYpPuPzVZiytGjpcG-Uu0qE2vAMDxBmaqs20gjqwE4O__dqmKRAhIZx2rNojzx1FDpNyOQ",
  userId: "btNUn3XDVgRtuEWa4K6MzPax7lk2"
};

// Save to localStorage
try {
  localStorage.setItem('customToken', loginData.customToken);
  localStorage.setItem('userId', loginData.userId);
  console.log('✅ Data saved successfully!');
} catch (error) {
  console.error('❌ Error saving to localStorage:', error);
}

// ===== STEP 3: VERIFY THE SAVE WORKED =====
console.log('\n🔍 VERIFICATION AFTER SAVE...\n');

const savedToken = localStorage.getItem('customToken');
const savedUserId = localStorage.getItem('userId');

console.log('📋 Verification Results:');
console.log('Token saved correctly:', savedToken === loginData.customToken ? '✅ YES' : '❌ NO');
console.log('UserId saved correctly:', savedUserId === loginData.userId ? '✅ YES' : '❌ NO');

if (savedToken) {
  console.log('📝 CustomToken details:');
  console.log('  → Length:', savedToken.length, 'characters');
  console.log('  → First 50 chars:', savedToken.substring(0, 50) + '...');
}

if (savedUserId) {
  console.log('👤 UserId:', savedUserId);
}

console.log('\n📦 Final localStorage status:');
console.log('customToken:', localStorage.getItem('customToken') ? 'SAVED ✅' : 'NOT FOUND ❌');
console.log('userId:', localStorage.getItem('userId') ? 'SAVED ✅' : 'NOT FOUND ❌');

// ===== STEP 4: PROVIDE HELPER FUNCTIONS =====
console.log('\n🔧 Helper functions available:');
console.log('  → checkLocalStorageStatus() - Check current status');
console.log('  → localStorage.getItem("customToken") - Get token');
console.log('  → localStorage.getItem("userId") - Get user ID');

// Make the check function available globally
window.checkLocalStorageStatus = checkLocalStorageStatus;
