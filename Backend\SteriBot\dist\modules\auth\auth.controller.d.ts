import { AuthService } from "./auth.service";
import { RegisterDto } from "./dto/register.dto";
import { LoginDto } from "./dto/login.dto";
import { UserProfileResponseDto } from "./dto/user-profile-response.dto";
export declare class AuthController {
    private authService;
    constructor(authService: AuthService);
    register(registerDto: RegisterDto): Promise<{
        message: string;
        userId: string;
    }>;
    login(loginDto: LoginDto): Promise<{
        message: string;
        customToken: string;
        instructions: string;
        user: {
            userId: string;
            email: any;
            username: any;
            role: any;
        };
    }>;
    getProfile(req: any): Promise<UserProfileResponseDto>;
}
