import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, Is<PERSON>, IsDateString } from "class-validator"
import { ApiProperty } from "@nestjs/swagger"

export class CreateRobotDto {
  @ApiProperty({
    description: 'Name of the robot',
    example: 'SteriBot-01'
  })
  @IsString()
  robotName: string

  @ApiProperty({
    description: 'Serial number of the robot',
    example: 'SB-2024-001'
  })
  @IsString()
  serialNumber: string

  @ApiProperty({
    required: false,
    description: 'Current battery level (0-100)',
    example: 85
  })
  @IsOptional()
  @IsNumber()
  batteryLevel?: number

  @ApiProperty({
    required: false,
    description: 'Current position/location of the robot',
    example: 'Charging Station A'
  })
  @IsOptional()
  @IsString()
  currentPosition?: string

  @ApiProperty({
    required: false,
    description: 'IP address of the robot',
    example: '*************'
  })
  @IsOptional()
  @IsIP()
  ipAddress?: string

  @ApiProperty({
    required: false,
    description: 'Firmware version',
    example: 'v2.1.3'
  })
  @IsOptional()
  @IsString()
  firmwareVersion?: string

  @ApiProperty({
    required: false,
    description: 'Last maintenance date',
    example: '2024-01-15T10:00:00.000Z'
  })
  @IsOptional()
  @IsDateString()
  maintenanceDate?: string

  @ApiProperty({
    required: false,
    description: 'ID of current task being executed',
    example: 'task123'
  })
  @IsOptional()
  @IsString()
  currentTaskId?: string
}
