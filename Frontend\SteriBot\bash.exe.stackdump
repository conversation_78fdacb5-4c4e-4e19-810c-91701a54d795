Stack trace:
Frame         Function      Args
0007FFFFA8F0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF97F0) msys-2.0.dll+0x1FE8E
0007FFFFA8F0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFABC8) msys-2.0.dll+0x67F9
0007FFFFA8F0  000210046832 (000210286019, 0007FFFFA7A8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA8F0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA8F0  000210068E24 (0007FFFFA900, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFABD0  00021006A225 (0007FFFFA900, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF94EB50000 ntdll.dll
7FF929510000 aswhook.dll
7FF94CE20000 KERNEL32.DLL
7FF94BE00000 KERNELBASE.dll
7FF948F40000 apphelp.dll
7FF94CF50000 USER32.dll
7FF94C5F0000 win32u.dll
7FF94D110000 GDI32.dll
7FF94C4C0000 gdi32full.dll
7FF94BD60000 msvcp_win.dll
7FF94BC40000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF94D550000 advapi32.dll
7FF94D840000 msvcrt.dll
7FF94D610000 sechost.dll
7FF94C1E0000 bcrypt.dll
7FF94D250000 RPCRT4.dll
7FF94B340000 CRYPTBASE.DLL
7FF94C6A0000 bcryptPrimitives.dll
7FF94EAC0000 IMM32.DLL
