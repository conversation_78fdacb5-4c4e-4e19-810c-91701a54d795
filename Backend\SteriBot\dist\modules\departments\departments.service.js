"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DepartmentsService = void 0;
const common_1 = require("@nestjs/common");
const firebase_service_1 = require("../../config/firebase/firebase.service");
let DepartmentsService = class DepartmentsService {
    constructor(firebaseService) {
        this.firebaseService = firebaseService;
        this.collection = "departments";
    }
    async create(createDepartmentDto) {
        const firestore = this.firebaseService.getFirestore();
        if (createDepartmentDto.parentDepartmentId) {
            const parentExists = await this.findById(createDepartmentDto.parentDepartmentId);
            if (!parentExists) {
                throw new common_1.BadRequestException(`Parent department with ID ${createDepartmentDto.parentDepartmentId} not found`);
            }
        }
        const existingDept = await this.findByName(createDepartmentDto.name);
        if (existingDept) {
            throw new common_1.BadRequestException(`Department with name '${createDepartmentDto.name}' already exists`);
        }
        const departmentData = {
            ...createDepartmentDto,
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        const docRef = await firestore.collection(this.collection).add(departmentData);
        return { id: docRef.id, ...departmentData };
    }
    async findAll() {
        const firestore = this.firebaseService.getFirestore();
        const snapshot = await firestore.collection(this.collection).orderBy('name').get();
        return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    }
    async findById(id) {
        const firestore = this.firebaseService.getFirestore();
        const doc = await firestore.collection(this.collection).doc(id).get();
        if (!doc.exists) {
            throw new common_1.NotFoundException(`Department with ID ${id} not found`);
        }
        return { id: doc.id, ...doc.data() };
    }
    async findByName(name) {
        const firestore = this.firebaseService.getFirestore();
        const snapshot = await firestore.collection(this.collection).where('name', '==', name).get();
        if (snapshot.empty) {
            return null;
        }
        const doc = snapshot.docs[0];
        return { id: doc.id, ...doc.data() };
    }
    async findChildren(parentId) {
        const firestore = this.firebaseService.getFirestore();
        const snapshot = await firestore.collection(this.collection)
            .where('parentDepartmentId', '==', parentId)
            .orderBy('name')
            .get();
        return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    }
    async update(id, updateDepartmentDto) {
        try {
            const firestore = this.firebaseService.getFirestore();
            const docRef = firestore.collection(this.collection).doc(id);
            const doc = await docRef.get();
            if (!doc.exists) {
                throw new common_1.NotFoundException(`Department with ID ${id} not found`);
            }
            if (updateDepartmentDto.parentDepartmentId) {
                if (updateDepartmentDto.parentDepartmentId === id) {
                    throw new common_1.BadRequestException("Department cannot be its own parent");
                }
                const parentExists = await this.findById(updateDepartmentDto.parentDepartmentId);
                if (!parentExists) {
                    throw new common_1.BadRequestException(`Parent department with ID ${updateDepartmentDto.parentDepartmentId} not found`);
                }
            }
            if (updateDepartmentDto.name) {
                const existingDept = await this.findByName(updateDepartmentDto.name);
                if (existingDept && existingDept.id !== id) {
                    throw new common_1.BadRequestException(`Department with name '${updateDepartmentDto.name}' already exists`);
                }
            }
            const updateData = {
                ...updateDepartmentDto,
                updatedAt: new Date(),
            };
            await docRef.update(updateData);
            return this.findById(id);
        }
        catch (error) {
            console.error('Error updating department:', error);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new Error(`Failed to update department: ${error.message}`);
        }
    }
    async remove(id) {
        try {
            const firestore = this.firebaseService.getFirestore();
            const doc = await firestore.collection(this.collection).doc(id).get();
            if (!doc.exists) {
                throw new common_1.NotFoundException(`Department with ID ${id} not found`);
            }
            const children = await this.findChildren(id);
            if (children.length > 0) {
                throw new common_1.BadRequestException("Cannot delete department that has child departments");
            }
            await firestore.collection(this.collection).doc(id).delete();
            return { message: "Department deleted successfully" };
        }
        catch (error) {
            console.error('Error deleting department:', error);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new Error(`Failed to delete department: ${error.message}`);
        }
    }
};
exports.DepartmentsService = DepartmentsService;
exports.DepartmentsService = DepartmentsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [firebase_service_1.FirebaseService])
], DepartmentsService);
//# sourceMappingURL=departments.service.js.map