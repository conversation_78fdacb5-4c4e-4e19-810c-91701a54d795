import { ApiProperty } from '@nestjs/swagger'

export class UpdatePhotoDto {
  @ApiProperty({
    type: 'string',
    format: 'binary',
    description: 'Profile photo file (JPEG, PNG, WebP)',
    required: true
  })
  photo: Express.Multer.File
}

export class UpdatePhotoResponseDto {
  @ApiProperty({
    description: 'Success message',
    example: 'Profile photo updated successfully'
  })
  message: string

  @ApiProperty({
    description: 'New photo URL',
    example: 'https://storage.googleapis.com/your-bucket/profile-pictures/user123_1234567890.jpg'
  })
  photoUrl: string

  @ApiProperty({
    description: 'User ID',
    example: 'user123'
  })
  userId: string
}
