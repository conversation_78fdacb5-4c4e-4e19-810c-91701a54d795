import { Injectable } from '@nestjs/common';
import { spawn } from 'child_process';
import * as path from 'path';
import * as WebSocket from 'ws';

export interface ROS2ConnectionResult {
  ip: string;
  status: 'connected' | 'failed';
}

export interface ROS2ConnectionResponse {
  ips: ROS2ConnectionResult[];
}

@Injectable()
export class ROS2ConnectionService {
  private readonly scanScriptPath = path.join(process.cwd(), 'Ros_api', 'scan_ros2.py');

  async connectToROS2IPs(): Promise<ROS2ConnectionResponse> {
    try {
      const ips = await this.getIPsFromScan();
      return { ips: await this.testConnections(ips) };
    } catch (error) {
      throw new Error(`ROS2 connection failed: ${error.message}`);
    }
  }

  healthCheck() {
    try {
      const fs = require('fs');
      const exists = fs.existsSync(this.scanScriptPath);
      return {
        status: exists ? 'healthy' : 'unhealthy',
        scan_script_exists: exists
      };
    } catch {
      return {
        status: 'unhealthy',
        scan_script_exists: false
      };
    }
  }

  private async getIPsFromScan(): Promise<string[]> {
    return new Promise<string[]>((resolve, reject) => {
      const timer = setTimeout(() => {
        proc.kill();
        reject(new Error('Scan timeout'));
      }, 15000);

      const proc = spawn('python', [this.scanScriptPath]);
      let output = '';

      proc.stdout.on('data', data => output += data);
      proc.stderr.once('data', data => console.debug(`Scan error: ${data}`));
      
      proc.on('close', code => {
        clearTimeout(timer);
        if (code !== 0) return reject(new Error(`Scan failed with code ${code}`));
        
        const ips = output
          .trim()
          .split('\n')
          .map(line => line.match(/ws:\/\/([^:]+):9090/)?.[1])
          .filter((ip): ip is string => !!ip);
          
        resolve(ips);
      });

      proc.on('error', reject);
    });
  }

  private async testConnections(ips: string[]): Promise<ROS2ConnectionResult[]> {
    if (ips.length === 0) return [];
    
    const results: ROS2ConnectionResult[] = [];
    const queue = [...ips];
    
    while (queue.length > 0) {
      const batch = queue.splice(0, 10);
      const batchResults = await Promise.all(
        batch.map(ip => this.testConnection(ip))
      );
      results.push(...batchResults);
    }
    
    return results;
  }

  private testConnection(ip: string): Promise<ROS2ConnectionResult> {
    return new Promise(resolve => {
      const timer = setTimeout(() => {
        resolve({ ip, status: 'failed' });
      }, 3000);

      const ws = new WebSocket(`ws://${ip}:9090`, { 
        handshakeTimeout: 2500,
        skipUTF8Validation: true 
      });

      ws.on('open', () => {
        clearTimeout(timer);
        ws.close();
        resolve({ ip, status: 'connected' });
      });

      ws.on('error', () => {
        clearTimeout(timer);
        resolve({ ip, status: 'failed' });
      });
    });
  }
}