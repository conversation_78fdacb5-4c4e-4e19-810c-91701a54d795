import { Injectable, NotFoundException, BadRequestException } from "@nestjs/common"
import { FirebaseService } from "../../config/firebase/firebase.service"
import type { CreateDepartmentDto } from "./dto/create-department.dto"
import type { UpdateDepartmentDto } from "./dto/update-department.dto"

@Injectable()
export class DepartmentsService {
  private readonly collection = "departments"

  constructor(private firebaseService: FirebaseService) {}

  async create(createDepartmentDto: CreateDepartmentDto) {
    const firestore = this.firebaseService.getFirestore()

    // Validate parent department exists if provided
    if (createDepartmentDto.parentDepartmentId) {
      const parentExists = await this.findById(createDepartmentDto.parentDepartmentId)
      if (!parentExists) {
        throw new BadRequestException(`Parent department with ID ${createDepartmentDto.parentDepartmentId} not found`)
      }
    }

    // Check if department name already exists
    const existingDept = await this.findByName(createDepartmentDto.name)
    if (existingDept) {
      throw new BadRequestException(`Department with name '${createDepartmentDto.name}' already exists`)
    }

    const departmentData = {
      ...createDepartmentDto,
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    const docRef = await firestore.collection(this.collection).add(departmentData)
    return { id: docRef.id, ...departmentData }
  }

  async findAll() {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore.collection(this.collection).orderBy('name').get()
    return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
  }

  async findById(id: string) {
    const firestore = this.firebaseService.getFirestore()
    const doc = await firestore.collection(this.collection).doc(id).get()

    if (!doc.exists) {
      throw new NotFoundException(`Department with ID ${id} not found`)
    }

    return { id: doc.id, ...doc.data() }
  }

  async findByName(name: string) {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore.collection(this.collection).where('name', '==', name).get()

    if (snapshot.empty) {
      return null
    }

    const doc = snapshot.docs[0]
    return { id: doc.id, ...doc.data() }
  }

  async findChildren(parentId: string) {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore.collection(this.collection)
      .where('parentDepartmentId', '==', parentId)
      .orderBy('name')
      .get()

    return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
  }

  async update(id: string, updateDepartmentDto: UpdateDepartmentDto) {
    try {
      const firestore = this.firebaseService.getFirestore()
      const docRef = firestore.collection(this.collection).doc(id)

      // Check if document exists first
      const doc = await docRef.get()
      if (!doc.exists) {
        throw new NotFoundException(`Department with ID ${id} not found`)
      }

      // Validate parent department exists if provided
      if (updateDepartmentDto.parentDepartmentId) {
        // Prevent circular reference
        if (updateDepartmentDto.parentDepartmentId === id) {
          throw new BadRequestException("Department cannot be its own parent")
        }

        const parentExists = await this.findById(updateDepartmentDto.parentDepartmentId)
        if (!parentExists) {
          throw new BadRequestException(`Parent department with ID ${updateDepartmentDto.parentDepartmentId} not found`)
        }
      }

      // Check if new name already exists (if name is being updated)
      if (updateDepartmentDto.name) {
        const existingDept = await this.findByName(updateDepartmentDto.name)
        if (existingDept && existingDept.id !== id) {
          throw new BadRequestException(`Department with name '${updateDepartmentDto.name}' already exists`)
        }
      }

      const updateData = {
        ...updateDepartmentDto,
        updatedAt: new Date(),
      }

      await docRef.update(updateData)
      return this.findById(id)
    } catch (error) {
      console.error('Error updating department:', error)
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error
      }
      throw new Error(`Failed to update department: ${error.message}`)
    }
  }

  async remove(id: string) {
    try {
      const firestore = this.firebaseService.getFirestore()

      // Check if department exists
      const doc = await firestore.collection(this.collection).doc(id).get()
      if (!doc.exists) {
        throw new NotFoundException(`Department with ID ${id} not found`)
      }

      // Check if department has children
      const children = await this.findChildren(id)
      if (children.length > 0) {
        throw new BadRequestException("Cannot delete department that has child departments")
      }

      // Check if department has job titles (we'll implement this check later)
      // TODO: Add check for associated job titles

      await firestore.collection(this.collection).doc(id).delete()
      return { message: "Department deleted successfully" }
    } catch (error) {
      console.error('Error deleting department:', error)
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error
      }
      throw new Error(`Failed to delete department: ${error.message}`)
    }
  }
}
